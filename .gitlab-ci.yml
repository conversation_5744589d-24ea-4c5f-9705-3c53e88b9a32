include:
  - project: 'plt/gitlab-pipeline-templates'
    file: socure-services.gitlab-ci.yml

variables:
  PROJECT_NAME: ai
  SERVICE_NAME: ai-gateway-service
  SERVICE_VERSION: 0.1.0
  MAVEN_EXTRA_ARGS: -Dbuild-environment=gitlab-ci -DskipTests -Dmaven.test.skip=true -Dscoverage.skip=true
  MAVEN_FIPS_ARGS: -Dbuild-environment=gitlab-ci-fips -DskipTests -Dmaven.test.skip=true -Dscoverage.skip=true
  MEND_PRODUCT_NAME: socure-saas
  DEV_ENVIRONMENT: "yes"
