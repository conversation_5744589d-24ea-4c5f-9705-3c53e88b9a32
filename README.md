# ai-gateway

Confluence Page - https://confluence.socure.com/pages/viewpage.action?pageId=234848742

## Project Structure

### Module Details

1. `ai-gateway-client` - it has the client code to connect to the gateway service. ID+ would be using this dependency
   for talking to this service.
2. `ai-gateway-common` - it has the models classes.
3. `ai-gateway-protobuf` - similar to `ai-gateway-common`, it has protobuf files containing messages and service
   definitions.
4. `ai-gateway-rest` - it has the classes (servlets, jetty service implementation etc) for the REST web service.
5. `ai-gateway-grpc` - it has the classes (service implementation) for the gRPC service.
6. `ai-gateway-vendor-service` - it has the implementation for connecting to downstream vendor level services.
7. `ai-gateway-service` - it has the main method for starting the services.
7. `ai-trice-service` - it has the classes for the Trice implementation

### Upstream Dependency

ID+ (aka Socure Service) is the upstream dependency for this gateway service.

### Downstream Dependencies

Vendor level services:
 1. ai-b<PERSON>mellon - BNY Mellon Vendor Service
 2. ai-microbilt - Microbilt Vendor Service
 3. kyc-vendor-service - Template Based REST Service for Calling Trice Vendor 

