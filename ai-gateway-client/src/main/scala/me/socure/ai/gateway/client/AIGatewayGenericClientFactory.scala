package me.socure.ai.gateway.client

import com.typesafe.config.Config
import me.socure.ai.gateway.common.models.{AIGatewayAuditedResponse, AIGatewayRequest}

import scala.concurrent.ExecutionContext
import scala.util.Try

object AIGatewayGenericClientFactory {

  def create(config: Config, useGatewayV2: Boolean = false)(implicit ec: ExecutionContext): AIGatewayGenericClient[AIGatewayRequest, AIGatewayAuditedResponse] = {
    val useGrpc = Try(config.getBoolean("ai.gateway.service.use.grpc")).getOrElse(false)
    if (useGrpc) {
      AIGatewayGrpcClientFactory.create(config)
    } else if (useGatewayV2) {
      AIGatewayRestClientFactory.createV2(config)
    } else {
      AIGatewayRestClientFactory.create(config)
    }
  }

}
