package me.socure.ai.gateway.client

import io.grpc.{Status, StatusRuntimeException}
import me.socure.ai.gateway.common.models.{AIGatewayAuditedResponse, AIGatewayRequest, AIGatewayResponse}
import me.socure.ai.gateway.grpc.resource.{Empty, HealthCheckResponse}
import me.socure.ai.gateway.grpc.service.GatewayServiceGrpc.GatewayServiceStub
import me.socure.common.data.core.provider._
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.microservice.client.auditing.AuditingInfo
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats}

import scala.concurrent.{ExecutionContext, Future}

class AIGatewayGrpcClient(gatewayServiceStub: GatewayServiceStub
                         )(implicit ec: ExecutionContext) extends AIGatewayGenericClient[AIGatewayRequest, AIGatewayAuditedResponse] {

  import AIGatewayGrpcClient._

  private def getBaseTags(apiName: String, accountId: Long, environmentId: String): MetricTags = {
    MetricTags(
      serviceName = Some("ai-gateway-service"),
      apiName = Some(apiName),
      isInternal = Some(true),
      mSource = Some("client"),
      accountId = Some(accountId),
      environmentId = Some(environmentId)
    )
  }

  def getAccountIntelligence(request: AIGatewayRequest): Future[AIGatewayAuditedResponse] = {

    val gatewayRequest = request.toGrpc
    val requestBody = Serialization.write(gatewayRequest)

    val transactionId = gatewayRequest.metadata.get.transactionId
    val accountId = gatewayRequest.metadata.get.accountId
    val environmentTypeId = gatewayRequest.metadata.get.environmentTypeId
    val environmentName = AIGatewayGenericClient.getEnvironmentName(environmentTypeId)

    val trxId: TrxId = TrxId(transactionId)
    val startTime = System.currentTimeMillis()

    val futureResponse = gatewayServiceStub.getAccountIntelligence(gatewayRequest)
      .withMetricTags(
        metrics = prefixedMetrics,
        baseTags = getBaseTags("getAccountIntelligence", accountId, environmentName)
      )()

    futureResponse.map { response =>
      val auditingInfo = AuditingInfo(
        url = "/getAccountIntelligence",
        requestBody = Some(requestBody),
        processingTime = System.currentTimeMillis() - startTime,
        responseBody = Option(response.toString),
        httpStatusCode = Status.OK.getCode.value
      )
      AIGatewayAuditedResponse(auditingInfo = auditingInfo, gatewayResponse = Right(AIGatewayResponse.from(response)))
    }.recover {
      case sre: StatusRuntimeException =>
        val auditingInfo = AuditingInfo(
          url = "/getAccountIntelligence",
          requestBody = Some(requestBody),
          processingTime = System.currentTimeMillis() - startTime,
          responseBody = Option(sre.getMessage),
          httpStatusCode = sre.getStatus.getCode.value
        )
        metrics.increment("get.account.intelligence.failure", s"accountId:$accountId", s"envTypeId:$environmentTypeId",
          s"exClass:${sre.getClass.getSimpleName}", s"exMsg:${sre.getMessage}", s"exCode:${sre.getStatus.getCode.value}")
        logger.error(s"Grpc StatusRuntimeException [${sre.getStatus.getCode.value}] in evaluating getAccountIntelligence" +
          s" for account $accountId envTypeId $environmentTypeId : ", sre)(trxId)
        AIGatewayAuditedResponse(auditingInfo = auditingInfo, gatewayResponse = Left(ErrorResponse(sre.getStatus.getCode.value, sre.getMessage)))

      case ex: Exception =>
        val auditingInfo = AuditingInfo(
          url = "/getAccountIntelligence",
          requestBody = Some(requestBody),
          processingTime = System.currentTimeMillis() - startTime,
          responseBody = Option(ex.getMessage),
          httpStatusCode = Status.UNKNOWN.getCode.value
        )
        val exClass = if (ex.getCause != null && ex.getCause.getClass != null) ex.getCause.getClass.getSimpleName else ex.getClass.getSimpleName
        metrics.increment("get.account.intelligence.failure",
          s"accountId:$accountId", s"envTypeId:$environmentTypeId", s"exClass:$exClass", s"exMsg:${ex.getMessage}")
        logger.error(s"Exception in evaluating getAccountIntelligence for account $accountId envTypeId $environmentTypeId : ", ex)(trxId)
        AIGatewayAuditedResponse(auditingInfo = auditingInfo, gatewayResponse = Left(ErrorResponse(Status.UNKNOWN.getCode.value, ex.getMessage)))
    }
  }

  def getHealthCheck: Future[HealthCheckResponse] = {
    gatewayServiceStub.healthCheck(Empty.defaultInstance)
  }

}

object AIGatewayGrpcClient {

  private implicit val jsonFormats: Formats = DefaultFormats
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)

}

