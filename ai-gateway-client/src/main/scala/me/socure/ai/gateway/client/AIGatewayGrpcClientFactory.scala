package me.socure.ai.gateway.client

import com.typesafe.config.Config
import io.grpc.ManagedChannelBuilder
import me.socure.ai.gateway.grpc.service.GatewayServiceGrpc
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext
import scala.util.Try

object AIGatewayGrpcClientFactory {

  private val logger: Logger = LoggerFactory.getLogger(getClass)

  def create(config: Config)(implicit ec: ExecutionContext): AIGatewayGrpcClient = {
    val useTLS = Try(config.getBoolean("ai.gateway.service.grpc.useTLS")).getOrElse(true)
    val channelBuilder = ManagedChannelBuilder.forAddress(
      config.getString("ai.gateway.service.grpc.host"),
      config.getInt("ai.gateway.service.grpc.port")
    )
    if (!useTLS) {
      logger.info("Using plaintext mode...")
      channelBuilder.usePlaintext
    } else logger.info("Using tls mode...")
    //channelBuilder.intercept(hmacGrpcClientInterceptor)
    new AIGatewayGrpcClient(GatewayServiceGrpc.stub(channelBuilder.build()))
  }
}
