package me.socure.ai.gateway.client

import com.typesafe.config.Config
import me.socure.common.hmac.filter.HttpWithHmacFactory

import scala.concurrent.ExecutionContext

object AIGatewayRestClientFactory {

  def create(config: Config)(implicit ec: ExecutionContext): AIGatewayRestClient = {
    val http = HttpWithHmacFactory.createInsecure(config = config.getConfig("ai.gateway.service.hmac"))
    new AIGatewayRestClient(http, config.getString("ai.gateway.service.endpoint"))
  }

  def createV2(config: Config)(implicit ec: ExecutionContext): AIGatewayRestClientV2 = {
    // V2 service doesn't use HMAC
    val http = HttpWithHmacFactory.createInsecure(config = config.getConfig("ai.gateway.service.hmac"))
    new AIGatewayRestClientV2(http, config.getString("ai.gateway.v2.service.endpoint"))
  }
}
