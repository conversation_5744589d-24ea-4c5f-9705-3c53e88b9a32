package me.socure.ai.gateway.client

import dispatch.{Http, url}
import me.socure.ai.gateway.common.models.{AIGatewayAuditedResponse, AIGatewayRequest, AIGatewayResponse}
import me.socure.common.data.core.provider._
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.microservice.client.MicroServiceResponseParser
import me.socure.common.microservice.client.auditing.AuditingInfo
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class AIGatewayRestClientV2(http: Http,
                          baseUrl: String
                         )(implicit ec: ExecutionContext) extends AIGatewayGenericClient[AIGatewayRequest, AIGatewayAuditedResponse] {

  import AIGatewayRestClientV2._

  private val safeEndpoint = baseUrl.stripSuffix("/")

  private def getBaseTags(apiName: String, accountId: Long, environmentId: String): MetricTags = {
    MetricTags(
      serviceName = Some("ai-gateway-service-v2"),
      apiName = Some(apiName),
      isInternal = Some(true),
      mSource = Some("client"),
      accountId = Some(accountId),
      environmentId = Some(environmentId)
    )
  }

  def getAccountIntelligence(aiGatewayRequest: AIGatewayRequest): Future[AIGatewayAuditedResponse] = {
    val requestBody = Serialization.write(aiGatewayRequest)
    val apiName = "/account-validation"
    val request = url(safeEndpoint + apiName)
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBodyEncoding(Charset.forName("UTF-8")) << requestBody
    val transactionId = aiGatewayRequest.metadata.transactionId
    val accountId = aiGatewayRequest.metadata.accountId
    val environmentTypeId = aiGatewayRequest.metadata.environmentTypeId
    val environmentName = AIGatewayGenericClient.getEnvironmentName(environmentTypeId)

    implicit val trxId: TrxId = TrxId(transactionId)
    val startTime = System.currentTimeMillis()

    val futureResponse = http(request)
      .withMetricTags(
        metrics = prefixedMetrics,
        baseTags = getBaseTags(apiName, accountId, environmentName),
        onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
      )()

    futureResponse.onFailure {
      case e =>
        val exClass = if (e.getCause != null && e.getCause.getClass != null) e.getCause.getClass.getSimpleName else e.getClass.getSimpleName
        metrics.increment("get.account.intelligence.failure",
          s"accountId:$accountId", s"envTypeId:$environmentTypeId", s"exClass:$exClass", s"exMsg:${e.getMessage}")
        logger.error(s"Error in evaluating getAccountIntelligence for account $accountId envTypeId $environmentTypeId : ", e)
    }

    futureResponse.map { response =>
      val responseStatus = response.getStatusCode
      val responseBody = response.getResponseBody
      val auditingInfo = AuditingInfo(
        url = request.toRequest.getUrl,
        requestBody = Some(requestBody),
        processingTime = System.currentTimeMillis() - startTime,
        responseBody = Option(responseBody),
        httpStatusCode = responseStatus
      )
      val gatewayResponse = Try {
        MicroServiceResponseParser.parseResponse[AIGatewayResponse](responseStatus, responseBody)
      } match {
        case Success(res) => res
        case Failure(ex) =>
          logger.error("Failed to parse ai-gateway-service-v2 getAccountIntelligence response", ex)
          Left(ErrorResponse(code = 199, message = "Parse error"))
      }
      AIGatewayAuditedResponse(auditingInfo = auditingInfo, gatewayResponse = gatewayResponse)
    }
  }
}

object AIGatewayRestClientV2 {
  private implicit val jsonFormats: Formats = DefaultFormats
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
}