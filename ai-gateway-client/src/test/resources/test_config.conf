#================ AI Gateway Service config ================#
ai.gateway.service {
  endpoint = "http://localhost:5000"
  hmac {
    realm="Socure"
    version="1.0"
    ttl=5
    time.interval=5
    strength=512
    aws.secrets.manager.id="socure-ai-gateway/stage/hmac"
    secret.refresh.interval=5000
  }
  grpc {
    #host = "ai-gateway-stage-parallel.socure.com"
    host = "localhost"
    #port = "443"
    port = "6000"
    useTLS = false
  }
  use.grpc = true
}
#================ AI Gateway Service config ================#