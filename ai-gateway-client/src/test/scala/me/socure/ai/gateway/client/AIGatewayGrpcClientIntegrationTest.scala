package me.socure.ai.gateway.client

import com.typesafe.config.ConfigFactory
import me.socure.ai.gateway.common.models.AIGatewayRequest
import org.json4s.jackson.JsonMethods.parse
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Ignore, Matchers}
import org.slf4j.LoggerFactory

import java.util.UUID
import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._

@Ignore
class AIGatewayGrpcClientIntegrationTest extends FunSuite with Matchers with ScalaFutures {

  private val logger = LoggerFactory.getLogger(this.getClass)

  private implicit def jsonFormats: Formats = DefaultFormats

  private val config = ConfigFactory.load("test_config.conf")

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(10, Seconds), interval = Span(500, Millis))

  private val gatewayGrpcClient = AIGatewayGrpcClientFactory.create(config)

  private def getAIGatewayRequest: AIGatewayRequest = {
    val inputJson =
      s"""{
         |    "metadata": {
         |        "accountId": 1,
         |        "environmentTypeId": 1,
         |        "maskPii": false,
         |        "transactionId": "${UUID.randomUUID().toString}"
         |    },
         |    "payment": {
         |        "accountNumber": "12345",
         |        "routingNumber": "*********",
         |        "inquiries": ["AVAILABILITY"]
         |    },
         |    "params": {
         |        "firstName": "John",
         |        "surName": "Abraham",
         |        "businessName": "Gaming"
         |    },
         |    "vendors": [
         |        {
         |            "name": "BNYML",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "MBTVL",
         |            "timeoutInMillis": 1200
         |        }
         |    ]
         |}""".stripMargin

    parse(inputJson).extract[AIGatewayRequest]
  }

  test("health check test") {
    val responseFuture = gatewayGrpcClient.getHealthCheck

    responseFuture.onFailure {
      case ex: Exception => fail(ex)
    }
    try {
      val result = Await.result(responseFuture, 30 second)
      assert(result.message.equalsIgnoreCase("success"))
    } catch {
      case ex: Exception => fail(ex)
    }
  }

  test("integration test") {
    assert(gatewayGrpcClient != null)

    val request = getAIGatewayRequest
    assert(request.metadata.accountId == 1)

    val responseFuture = gatewayGrpcClient.getAccountIntelligence(request)

    responseFuture.onFailure {
      case ex: Exception => fail(ex)
    }

    try {
      val result = Await.result(responseFuture, 30 second)
      assert(result.auditingInfo.httpStatusCode == 0)
      result.gatewayResponse match {
        case Left(error) =>
          logger.info(s"Error response received : ${error}")
          fail(error.message)
        case Right(res) =>
          logger.info(s"Success response received : ${res}")
          assert(res.status == "SUCCESS")
          assert(res.vendorResponses.size == 2)
      }
    } catch {
      case ex: Exception => fail(ex)
    }
  }
}
