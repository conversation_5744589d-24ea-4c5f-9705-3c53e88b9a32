<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-gateway</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ai-gateway-common</artifactId>
    <version>${revision}</version>
    <properties>
        <socure.commons.version>${revision}</socure.commons.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-transaction-aware-logger</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-logs</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice-client</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-gateway-protobuf</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nrinaudo</groupId>
            <artifactId>kantan.csv_2.11</artifactId>
            <version>0.5.1</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb-enhanced</artifactId>
            <version>${aws.java.sdk.v2.version}</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
            <version>3.1.0-RC2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_${sc.ver}</artifactId>
            <version>3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_${sc.ver}</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <!--<build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>-->
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
