package me.socure.ai.gateway.common.cache.impl

import com.typesafe.config.Config
import me.socure.ai.gateway.common.cache.{CacheService, CacheValue}
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.transaction.id.TrxId
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.{AttributeValue, DeleteItemRequest, GetItemRequest, PutItemRequest}

import java.time.{Instant, LocalDateTime, ZoneId}
import scala.collection.JavaConverters._
import scala.util.{Failure, Success, Try}

/**
 * DynamoDB Based Cache Service
 * AWS Reference Docs:
 * https://docs.aws.amazon.com/sdk-for-java/latest/developer-guide/examples-dynamodb-items.html
 *
 * @param config         The config object that contains Dynamo Cache Information
 * @param dynamoDbClient The DynamoDB client for performing the operations
 */
class DynamoCacheService(
                          config: Config,
                          dynamoDbClient: DynamoDbClient
                        )
  extends CacheService {
  import DynamoCacheService._

  val TableName = config.getString("table")

  val PartitionKey = config.getString("partitionKey")
  val TransferIdKey = "transferId"
  val TransferTypeKey = "transferType"
  val CreationTimestampKey = "createdOn"
  val TtlKey = config.getString("ttlKey")

  private val log = TransactionAwareLoggerFactory.getLogger(this.getClass)

  override def get(id: String)(implicit trxId: TrxId): Option[CacheValue] = {
    // 1. Query Dynamo for Key
    val key = Map(PartitionKey -> AttributeValue.builder().s(id).build()).asJava
    val request = GetItemRequest.builder().tableName(TableName).key(key).build()
    val response = dynamoDbClient.getItem(request).item()
    // 2. Interpret Values Returned as Cache Miss or Hit
    // 2.1 Cache Miss - No response, Some response without transferId, Expired Cache Value
    if (response == null || response.isEmpty || response.get("transferId").s().isEmpty || isItemExpired(response.get(TtlKey).n().toLong)) {
      log.debug("Cache Miss for DynamoDB Cache")
      None
    }
    // 2.2 Cache Hit - Return Transfer Id Details
    else {
      log.info("Cache Hit for Trice DynamoDB Cache")
      Some(
        CacheValue(
          transferId = response.get(TransferIdKey).s(),
          transferType = response.get(TransferTypeKey).s(),
          expiresOn = Some(response.get(TtlKey).n().toLong),
          createdOn = Some(response.get(CreationTimestampKey).n().toLong)
        )
      )
    }
  }

  override def set(id: String, value: CacheValue)(implicit trxId: TrxId): Unit = {
    // 1. Update or insert handled by AWS; ttl is automatically handled by this class and picked up from the config
    val itemValues = Map(
      PartitionKey -> AttributeValue.builder().s(id).build(),
      TransferIdKey -> AttributeValue.builder().s(value.transferId).build(),
      TransferTypeKey -> AttributeValue.builder().s(value.transferType).build(),
      TtlKey -> AttributeValue.builder().n(createExpiryHours(config.getLong("ttl"))).build(),
      CreationTimestampKey -> AttributeValue.builder().n(LocalDateTime.now().atZone(ZoneId.of("UTC")).toEpochSecond.toString).build()
    ).asJava
    val request = PutItemRequest.builder().tableName(TableName).item(itemValues).build()
    Try(dynamoDbClient.putItem(request)) match {
      case Success(response) =>
        log.info("Successfully updated DynamoDB cache")
      case Failure(e) =>
        log.error("Unable to insert cache value in DynamoDB", e)
    }
  }

  override def delete(id: String)(implicit trxId: TrxId): Unit = {
    val itemToDelete = Map(
      PartitionKey -> AttributeValue.builder().s(id).build()
    ).asJava
    val request = DeleteItemRequest.builder()
      .tableName(TableName)
      .key(itemToDelete)
      .build();
    Try(dynamoDbClient.deleteItem(request)) match {
      case Success(response) =>
        log.error("Successfully updated DynamoDB cache")
      case Failure(e) =>
        log.error("Unable to insert cache value in DynamoDB", e)
    }
  }
}

object DynamoCacheService {
  /**
   * Validates if a given Epoch Timestamp in Long occurs after the current time
   * @param expiresOn The Epoch Timestamp
   * @return true or false
   */
  def isItemExpired(expiresOn: Long): Boolean = {
    val now = LocalDateTime.now()
    now.isAfter(
      LocalDateTime.ofInstant(Instant.ofEpochSecond(expiresOn), ZoneId.of("UTC"))
    )
  }

  /**
   * Generates an expiry timestamp in Epoch format by adding a fixed number of hours to the currentTime
   * @return a string representation of the expiry Epoch Timestamp
   */
  def createExpiryHours(hoursToAdd: Long): String = {
    LocalDateTime.now().plusHours(hoursToAdd).atZone(ZoneId.of("UTC")).toEpochSecond.toString
  }
}
