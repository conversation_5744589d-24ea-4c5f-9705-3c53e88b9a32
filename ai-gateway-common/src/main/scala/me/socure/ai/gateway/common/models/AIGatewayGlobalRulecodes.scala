package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.GlobalRulecodes

final case class AIGatewayGlobalRulecodes(
                                           name: String = AIGatewayGlobalRulecodes.global,
                                           numericalRulecodes: Map[String, Double],
                                           categoricalRulecodes: Map[String, String]
                                         ) {

  def toGrpc: GlobalRulecodes = {
    GlobalRulecodes.defaultInstance
      .withName(this.name)
      .withNumericalRulecodes(this.numericalRulecodes)
      .withCategoricalRulecodes(this.categoricalRulecodes)
  }
}

object AIGatewayGlobalRulecodes {
  val global = "GLOBAL"
}