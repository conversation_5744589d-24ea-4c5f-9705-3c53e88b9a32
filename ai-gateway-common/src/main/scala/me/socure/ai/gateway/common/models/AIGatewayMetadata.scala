package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.MetaData

final case class AIGatewayMetadata(
                                      accountId: Long,
                                      environmentTypeId: Int,
                                      maskPii: Boolean,
                                      transactionId: String,
                                      memo: Option[String],
                                      ultimateSendingPartyId: Option[String],
                                      submissionDate: Option[Long]
                                  ) {
    def toGrpc: MetaData = {
        MetaData.defaultInstance
                .withAccountId(this.accountId)
                .withEnvironmentTypeId(this.environmentTypeId)
                .withMaskPii(this.maskPii)
                .withTransactionId(this.transactionId)
                .withMemo(this.memo.getOrElse(""))
                .withUltimateSendingPartyId(this.ultimateSendingPartyId.getOrElse(""))
                .withSubmissionDate(this.submissionDate.getOrElse(0L))
    }
}

object AIGatewayMetadata {

    def from(metadata: MetaData): AIGatewayMetadata = {
        AIGatewayMetadata(
            accountId = metadata.accountId,
            environmentTypeId = metadata.environmentTypeId,
            maskPii = metadata.maskPii,
            transactionId = metadata.transactionId,
            memo = Some(metadata.memo),
            ultimateSendingPartyId = Some(metadata.ultimateSendingPartyId),
            submissionDate = Some(metadata.submissionDate)
            )
    }

}