package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.Params

final case class AIGatewayParams(
                                  firstName: Option[String],
                                  surName: Option[String],
                                  businessName: Option[String],
                                  physicalAddress: Option[String],
                                  physicalAddress2: Option[String],
                                  city: Option[String],
                                  state: Option[String],
                                  zip: Option[String],
                                  country: Option[String],
                                  nationalId: Option[String],
                                  mobileNumber: Option[String],
                                  dob: Option[String],
                                  businessEin: Option[String],
                                  businessPhone: Option[String] = None,
                                  email: Option[String] = None
                                ) {
  def toGrpc: Params = {
    Params.defaultInstance
      .withFirstName(this.firstName.getOrElse(""))
      .withSurName(this.surName.getOrElse(""))
      .withBusinessName(this.businessName.getOrElse(""))
      .withPhysicalAddress(this.physicalAddress.getOrElse(""))
      .withPhysicalAddress2(this.physicalAddress2.getOrElse(""))
      .withCity(this.city.getOrElse(""))
      .withState(this.state.getOrElse(""))
      .withZip(this.zip.getOrElse(""))
      .withCountry(this.country.getOrElse(""))
      .withNationalId(this.nationalId.getOrElse(""))
      .withMobileNumber(this.mobileNumber.getOrElse(""))
      .withDob(this.dob.getOrElse(""))
      .withBusinessEin(this.businessEin.getOrElse(""))
      .withEmail(this.email.getOrElse(""))
  }
}

object AIGatewayParams {
  val empty: AIGatewayParams = AIGatewayParams(
    firstName = None,
    surName = None,
    businessName = None,
    physicalAddress = None,
    physicalAddress2 = None,
    city = None,
    state = None,
    zip = None,
    country = None,
    nationalId = None,
    mobileNumber = None,
    businessPhone = None,
    dob = None,
    businessEin = None,
    email = None
  )

  def from(paramsOpt: Option[Params]): AIGatewayParams = {

    def optionalOf(s: String): Option[String] = {
      if (s.trim.isEmpty) None
      else Some(s.trim)
    }

    paramsOpt match {
      case Some(params) =>
        AIGatewayParams(
          firstName = optionalOf(params.firstName),
          surName = optionalOf(params.surName),
          businessName = optionalOf(params.businessName),
          physicalAddress = optionalOf(params.physicalAddress),
          physicalAddress2 = optionalOf(params.physicalAddress2),
          city = optionalOf(params.city),
          state = optionalOf(params.state),
          zip = optionalOf(params.zip),
          country = optionalOf(params.country),
          nationalId = optionalOf(params.nationalId),
          mobileNumber = optionalOf(params.mobileNumber),
          dob = optionalOf(params.dob),
          businessEin = optionalOf(params.businessEin),
          // Passing None as we are currently excluding gRPC from the build.
          businessPhone = None,
          email = optionalOf(params.email)
        )
      case None => AIGatewayParams.empty
    }

  }
}
