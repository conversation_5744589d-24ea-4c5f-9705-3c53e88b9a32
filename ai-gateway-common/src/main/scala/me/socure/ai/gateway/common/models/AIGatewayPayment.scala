package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.Payment

final case class AIGatewayPayment(
                                   accountNumber: String,
                                   routingNumber: String,
                                   accountCountry: String,
                                   inquiries: Set[String]
                                 ) {
  def toGrpc: Payment = {
    Payment.defaultInstance
      .withAccountNumber(this.accountNumber)
      .withRoutingNumber(this.routingNumber)
      .withInquiries(this.inquiries.toSeq)
  }
}

object AIGatewayPayment {
  def from(payment: Payment): AIGatewayPayment = {
    AIGatewayPayment(
      accountNumber = payment.accountNumber,
      routingNumber = payment.routingNumber,
      accountCountry = payment.accountCountry,
      inquiries = payment.inquiries.toSet
    )
  }
}
