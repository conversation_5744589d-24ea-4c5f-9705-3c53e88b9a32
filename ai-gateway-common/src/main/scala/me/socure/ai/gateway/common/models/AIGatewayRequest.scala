package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.common.models.AIGatewayInquiries.{AVAILABILITY, STATUS}
import me.socure.ai.gateway.common.models.AIGatewayRequest.phoneFilterSet
import me.socure.ai.gateway.common.models.vendor.AIVendorRequest
import me.socure.ai.gateway.grpc.resource.{GatewayRequest, VendorConfig, VendorRequest}

final case class AIGatewayRequest(
                                   metadata: AIGatewayMetadata,
                                   payment: AIGatewayPayment,
                                   params: AIGatewayParams,
                                   vendors: Seq[AIGatewayVendorConfig]
                                 ) {
  def toVendorRequestGrpc: VendorRequest = {
    VendorRequest.defaultInstance
      .withAccountId(this.metadata.accountId)
      .withEnvironmentTypeId(this.metadata.environmentTypeId)
      .withMaskPii(this.metadata.maskPii)
      .withTransactionId(this.metadata.transactionId)
      .withAccountNumber(this.payment.accountNumber)
      .withRoutingNumber(this.payment.routingNumber)
      .withInquiries(updateInquiries(this.payment.inquiries).toSeq)
      .withFirstName(this.params.firstName.getOrElse(""))
      .withSurName(this.params.surName.getOrElse(""))
      .withBusinessName(this.params.businessName.getOrElse(""))
      .withPhysicalAddress(this.params.physicalAddress.getOrElse(""))
      .withPhysicalAddress2(this.params.physicalAddress2.getOrElse(""))
      .withCity(this.params.city.getOrElse(""))
      .withState(this.params.state.getOrElse(""))
      .withZip(this.params.zip.getOrElse(""))
      .withNationalId(this.params.nationalId.getOrElse(""))
      .withMobileNumber(this.params.mobileNumber.getOrElse(""))
      .withDob(this.params.dob.getOrElse(""))
      .withCountry(this.params.country.getOrElse(""))
      .withEin(this.params.businessEin.getOrElse(""))
      .withSubmissionDate(this.metadata.submissionDate.getOrElse(0L))
  }

  def toVendorRequest: AIVendorRequest = {
    AIVendorRequest(
      accountId = this.metadata.accountId,
      environmentTypeId = this.metadata.environmentTypeId,
      maskPii = this.metadata.maskPii,
      transactionId = this.metadata.transactionId,
      accountNumber = this.payment.accountNumber,
      routingNumber = this.payment.routingNumber,
      accountCountry = if (this.payment.accountCountry.trim.nonEmpty) None else Some(this.payment.accountCountry),
      inquiries = updateInquiries(this.payment.inquiries),
      firstName = this.params.firstName,
      surName = this.params.surName,
      businessName = this.params.businessName,
      physicalAddress = this.params.physicalAddress,
      physicalAddress2 = this.params.physicalAddress2,
      acceptanceCriteria = None,
      city = this.params.city,
      state = this.params.state,
      zip = this.params.zip,
      nationalId = this.params.nationalId,
      mobileNumber = resolvePhoneValue,
      dob = this.params.dob,
      country = this.params.country,
      ein = this.params.businessEin,
      email = this.params.email,
      memo = this.metadata.memo,
      ultimateSendingPartyId = this.metadata.ultimateSendingPartyId,
      submissionDate = this.metadata.submissionDate
      )
  }

  /**
   * If businessName is present in the request along with the businessPhone, then override with the businessPhone value.
   * */
  def resolvePhoneValue: Option[String] = {
    var phoneVal = this.params.mobileNumber
    if(this.params.businessName.isDefined && this.params.businessPhone.isDefined){
      phoneVal = this.params.businessPhone
    }
    phoneVal.filterNot(phoneFilterSet.contains)
  }

  private def updateInquiries(inquiries: Set[String]): Set[String] = {
    val updatedInquiries = if (inquiries.contains(AIGatewayInquiries.STATUS)) {
      inquiries.filterNot(_.equalsIgnoreCase(AIGatewayInquiries.STATUS)) ++ Set(AIGatewayInquiries.AVAILABILITY)
    } else inquiries
    updatedInquiries
  }

  def toGrpc: GatewayRequest = {
    val vendors = this.vendors.map(v => VendorConfig.defaultInstance.withName(v.name).withTimeoutInMillis(v.timeoutInMillis))

    GatewayRequest.defaultInstance
      .withMetadata(this.metadata.toGrpc)
      .withPayment(this.payment.toGrpc)
      .withParams(this.params.toGrpc)
      .withVendors(vendors)
  }
}

object AIGatewayRequest {
  // This is used to filter out any pre filled phone numbers as we dont need to send them to the downstream services
  val phoneFilterSet: Set[String] = Set("***********", "***********")
  def from(gatewayRequest: GatewayRequest): AIGatewayRequest = {
    def convertVendorConfig(vendorConfig: Seq[VendorConfig]): Seq[AIGatewayVendorConfig] = {
      vendorConfig.map { vendor =>
        AIGatewayVendorConfig(vendor.name, vendor.timeoutInMillis)
      }
    }

    AIGatewayRequest(
      metadata = AIGatewayMetadata.from(gatewayRequest.metadata.get),
      params = AIGatewayParams.from(gatewayRequest.params),
      payment = AIGatewayPayment.from(gatewayRequest.payment.get),
      vendors = convertVendorConfig(gatewayRequest.vendors)
    )
  }
}