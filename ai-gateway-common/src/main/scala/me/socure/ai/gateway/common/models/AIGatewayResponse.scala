package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.common.models.vendor.AIVendorResponse
import me.socure.ai.gateway.grpc.resource.GatewayResponse

final case class AIGatewayResponse(
                                    scores: AIGatewayScore,
                                    status: String,
                                    vendorResponses: Seq[AIVendorResponse],
                                    globalRulecodes: Option[AIGatewayGlobalRulecodes],
                                    reasonCodes: Set[String] = Set.empty[String]
                                  )

object AIGatewayResponse {
  def from(gatewayResponse: GatewayResponse): AIGatewayResponse = {
    AIGatewayResponse(
      scores = AIGatewayScore.from(gatewayResponse.getScore),
      status = gatewayResponse.status,
      vendorResponses = gatewayResponse.vendorResponses.map(AIVendorResponse.from),
      globalRulecodes = gatewayResponse.globalRulecodes.map(g =>
        AIGatewayGlobalRulecodes(
          name = g.name,
          numericalRulecodes = g.numericalRulecodes,
          categoricalRulecodes = g.categoricalRulecodes
        )
      ),
      reasonCodes = gatewayResponse.reasonCodes.toSet
    )
  }
}
