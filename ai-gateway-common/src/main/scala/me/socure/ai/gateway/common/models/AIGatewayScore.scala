package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.GatewayScore

final case class AIGatewayScore(
                                   availabilityScore: Option[Double],
                                   ownershipScore: Option[Double],
                                   availabilityScoreBreakDown: Option[AIGatewayScoreBreakdown],
                                   ownershipScoreBreakDown: Option[AIGatewayScoreBreakdown]
                               ) {
}

object AIGatewayScore {
    def from(gatewayScore: GatewayScore): AIGatewayScore = {
        AIGatewayScore(
            Some(gatewayScore.availabilityScore),
            Some(gatewayScore.ownershipScore),
            Some(AIGatewayScoreBreakdown.from(gatewayScore.availabilityScoreBreakdown)),
            Some(AIGatewayScoreBreakdown.from(gatewayScore.ownershipScoreBreakdown))
            )
    }
}
