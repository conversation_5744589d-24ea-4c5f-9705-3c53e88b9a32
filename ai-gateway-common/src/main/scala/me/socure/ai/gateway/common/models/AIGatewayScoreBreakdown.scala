package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.GatewayScoreBreakdown

case class AIGatewayScoreBreakdown(
                                      mbltScore: Option[Double] = None,
                                      bnymScore: Option[Double] = None,
                                      triceScore: Option[Double] = None,
                                      convlScore: Option[Double] = None
                                  )

object AIGatewayScoreBreakdown {
    def from(gatewayScoreBreakDown: Option[GatewayScoreBreakdown]): AIGatewayScoreBreakdown = {
        gatewayScoreBreakDown match {
            case Some(breakdown) => AIGatewayScoreBreakdown(
                Some(breakdown.mbltScore),
                Some(breakdown.bnymScore),
                Some(breakdown.triceScore))
            case _ => AIGatewayScoreBreakdown()
        }
    }
}
