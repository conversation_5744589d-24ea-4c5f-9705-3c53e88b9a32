package me.socure.ai.gateway.common.models

import me.socure.ai.gateway.grpc.resource.Name

object AIGatewayVendors extends Enumeration {

  val BNYVL = Value("BNYVL")
  val MBTVL = Value("MBTVL")
  val SOCVL = Value("SOCVL")
  val CONVL = Value("CONVL")
  val VRVAL = Value("VRVAL")

  def getNameFromValue(name: String): Name =
    if (name.equalsIgnoreCase(MBTVL.toString) || name.equalsIgnoreCase(Name.MICROBILT.name))
      Name.MICROBILT
    else if (name.equalsIgnoreCase(BNYVL.toString) || name.equalsIgnoreCase(Name.BNYMELLON.name))
      Name.BNYMELLON
    else if (name.equalsIgnoreCase(CONVL.toString) || name.equalsIgnoreCase(Name.CONVL.name))
      Name.CONVL
    else if(name.equalsIgnoreCase(VRVAL.toString) || name.equalsIgnoreCase(Name.VRVAL.name))
      Name.VRVAL
    else
      Name.SOCUREID
}