package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.grpc.resource.VendorError

final case class AIVendorError(code: String, description: String) {
  def toGrpc: VendorError = {
    VendorError.defaultInstance.withCode(code).withDescription(description)
  }
}

object AIVendorError {

  def from(vendorError: Option[VendorError]): Option[AIVendorError] =
    vendorError match {
      case Some(error) =>
        Some(AIVendorError(
          code = error.code,
          description = error.description
        ))
      case None => None
    }
}
