package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.common.models.AIGatewayVendors
import me.socure.ai.gateway.grpc.resource.VendorMetadata

final case class AIVendorMetadata(name: String,
                                  success: Boolean,
                                  statusCode: String,
                                  vendorReferenceId: String,
                                  transactionId: String) {
  def toGrpc: VendorMetadata = {
    VendorMetadata.defaultInstance
      .withName(AIGatewayVendors.getNameFromValue(this.name))
      .withSuccess(this.success)
      .withStatusCode(this.statusCode)
      .withVendorReferenceId(this.vendorReferenceId)
      .withTransactionId(this.transactionId)
  }
}

object AIVendorMetadata {
  def from(vendorMetadata: Option[VendorMetadata]): AIVendorMetadata = {
    AIVendorMetadata(
      name = vendorMetadata.map(_.name.name).getOrElse(""),
      success = vendorMetadata.exists(_.success),
      statusCode = vendorMetadata.map(_.statusCode).getOrElse(""),
      vendorReferenceId = vendorMetadata.map(_.vendorReferenceId).getOrElse(""),
      transactionId = vendorMetadata.map(_.transactionId).getOrElse("")
    )
  }
}