package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.grpc.resource.VendorRequest

final case class AIVendorRequest(accountId: Long,
                                 environmentTypeId: Int,
                                 maskPii: Boolean,
                                 transactionId: String,
                                 accountNumber: String,
                                 routingNumber: String,
                                 accountCountry: Option[String],
                                 inquiries: Set[String],
                                 firstName: Option[String],
                                 surName: Option[String],
                                 businessName: Option[String],
                                 physicalAddress: Option[String],
                                 physicalAddress2: Option[String],
                                 acceptanceCriteria: Option[String],
                                 city: Option[String],
                                 state: Option[String],
                                 zip: Option[String],
                                 nationalId: Option[String],
                                 mobileNumber: Option[String],
                                 dob: Option[String],
                                 country: Option[String],
                                 ein: Option[String],
                                 email: Option[String],
                                 memo: Option[String],
                                 ultimateSendingPartyId: Option[String],
                                 submissionDate: Option[Long]
                                ) {

  def toGrpc: VendorRequest = {
    VendorRequest.defaultInstance
      .withAccountId(this.accountId)
      .withEnvironmentTypeId(this.environmentTypeId)
      .withMaskPii(this.maskPii)
      .withTransactionId(this.transactionId)
      .withAccountNumber(this.accountNumber)
      .withRoutingNumber(this.routingNumber)
      .withInquiries(this.inquiries.toSeq)
      .withFirstName(this.firstName.getOrElse(""))
      .withSurName(this.surName.getOrElse(""))
      .withBusinessName(this.businessName.getOrElse(""))
      .withPhysicalAddress(this.physicalAddress.getOrElse(""))
      .withPhysicalAddress2(this.physicalAddress2.getOrElse(""))
      .withAcceptanceCriteria(this.acceptanceCriteria.getOrElse(""))
      .withCity(this.city.getOrElse(""))
      .withState(this.state.getOrElse(""))
      .withZip(this.zip.getOrElse(""))
      .withCountry(this.country.getOrElse(""))
      .withNationalId(this.nationalId.getOrElse(""))
      .withMobileNumber(this.mobileNumber.getOrElse(""))
      .withDob(this.dob.getOrElse(""))
      .withEin(this.ein.getOrElse(""))
      .withEmail(this.email.getOrElse(""))
      .withMemo(this.memo.getOrElse(""))
      .withUltimateSendingPartyId(this.ultimateSendingPartyId.getOrElse(""))
      .withSubmissionDate(this.submissionDate.getOrElse(0L))
  }
}

object AIVendorRequest {
  def from(vendorRequest: VendorRequest): AIVendorRequest = {

    def optionalOf(s: String): Option[String] = {
      if (s.trim.isEmpty) None
      else Some(s.trim)
    }

    AIVendorRequest(
      accountId = vendorRequest.accountId,
      environmentTypeId = vendorRequest.environmentTypeId,
      maskPii = vendorRequest.maskPii,
      transactionId = vendorRequest.transactionId,
      accountNumber = vendorRequest.accountNumber,
      routingNumber = vendorRequest.routingNumber,
      accountCountry = optionalOf(vendorRequest.accountCountry),
      inquiries = vendorRequest.inquiries.toSet,
      firstName = optionalOf(vendorRequest.firstName),
      surName = optionalOf(vendorRequest.surName),
      businessName = optionalOf(vendorRequest.businessName),
      physicalAddress = optionalOf(vendorRequest.physicalAddress),
      physicalAddress2 = optionalOf(vendorRequest.physicalAddress2),
      acceptanceCriteria = optionalOf(vendorRequest.acceptanceCriteria),
      city = optionalOf(vendorRequest.city),
      state = optionalOf(vendorRequest.state),
      zip = optionalOf(vendorRequest.zip),
      nationalId = optionalOf(vendorRequest.nationalId),
      mobileNumber = optionalOf(vendorRequest.mobileNumber),
      dob = optionalOf(vendorRequest.dob),
      country = optionalOf(vendorRequest.country),
      ein = optionalOf(vendorRequest.ein),
      email = optionalOf(vendorRequest.email),
      memo = optionalOf(vendorRequest.memo),
      ultimateSendingPartyId = optionalOf(vendorRequest.ultimateSendingPartyId),
      submissionDate = Option(vendorRequest.submissionDate)
    )
  }
}
