package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.grpc.resource.VendorResponse

final case class AIVendorResponse(metadata: AIVendorMetadata,
                                  result: AIVendorResult,
                                  error: Option[AIVendorError] = None,
                                  rulecodes: Option[AIVendorRuleCodes] = None) {
  def toGrpc: VendorResponse = {
    if (error.isDefined && rulecodes.isDefined) {
      VendorResponse.defaultInstance
        .withMetadata(metadata.toGrpc)
        .withResult(result.toGrpc)
        .withVendorError(error.get.toGrpc)
        .withRulecodes(rulecodes.get.toGrpc)
    } else if (error.isDefined) {
      VendorResponse.defaultInstance
        .withMetadata(metadata.toGrpc)
        .withResult(result.toGrpc)
        .withVendorError(error.get.toGrpc)
    } else if (rulecodes.isDefined) {
      VendorResponse.defaultInstance
        .withMetadata(metadata.toGrpc)
        .withResult(result.toGrpc)
        .withRulecodes(rulecodes.get.toGrpc)
    } else {
      VendorResponse.defaultInstance
    }
  }
}

object AIVendorResponse {
  def from(vendorResponse: VendorResponse): AIVendorResponse = {
    AIVendorResponse(
      metadata = AIVendorMetadata.from(vendorResponse.metadata),
      result = AIVendorResult.from(vendorResponse.result),
      error = AIVendorError.from(vendorResponse.vendorError),
      rulecodes = vendorResponse.rulecodes.map(vRc =>
        AIVendorRuleCodes(
          numericalRulecodes = vRc.numericalRulecodes,
          categoricalRulecodes = vRc.categoricalRulecodes
        )
      )
    )
  }
}
