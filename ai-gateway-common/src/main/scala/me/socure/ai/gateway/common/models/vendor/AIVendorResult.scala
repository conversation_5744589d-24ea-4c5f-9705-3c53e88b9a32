package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.grpc.resource.{Result, AccountOwnership => ProtoAccountOwnership, AccountStatus => ProtoAccountStatus}

final case class AIVendorResult(ownership: Option[AccountOwnership],
                                status: Option[AccountStatus]
                               ) {
  def toGrpc: Result = {
    if (ownership.isDefined && status.isDefined) {
      Result.defaultInstance
        .withStatus(
          ProtoAccountStatus.defaultInstance
            .withCode(status.get.code)
            .withDescription(status.get.description)
            .withEwsDirectContributor(status.get.ewsDirectContributor match {
              case Some(x: String) => x
            })
            .withMbLastSeen(status.get.mbLastSeen match {
              case Some(x: String) => x
            })
            .withMbDecisionCode(status.get.mbDecisionCode match {
              case Some(x: String) => x
            })
        )
        .withOwnership(
          ProtoAccountOwnership.defaultInstance
            .withCode(ownership.get.code)
            .withDescription(ownership.get.description)
        )

    } else if (ownership.isDefined) {
      Result.defaultInstance
        .withOwnership(
          ProtoAccountOwnership.defaultInstance
            .withCode(ownership.get.code)
            .withDescription(ownership.get.description)
        )
    } else if (status.isDefined) {
      Result.defaultInstance
        .withStatus(
          ProtoAccountStatus.defaultInstance
            .withCode(status.get.code)
            .withDescription(status.get.description)
            .withEwsDirectContributor(status.get.ewsDirectContributor match {
              case Some(x: String) => x
            })
            .withMbLastSeen(status.get.mbLastSeen match {
              case Some(x: String) => x
            })
            .withMbDecisionCode(status.get.mbDecisionCode match {
              case Some(x: String) => x
            })
        )
    } else {
      Result.defaultInstance
    }
  }
}

object AIVendorResult {
  def from(vendorResult: Option[Result]): AIVendorResult = {
    vendorResult match {
      case Some(res) =>
        if (res.ownership.isDefined && res.status.isDefined) {
          AIVendorResult(
            ownership = Some(
              AccountOwnership(
                code = vendorResult.flatMap(_.ownership).map(_.code).getOrElse(""),
                description = vendorResult.flatMap(_.ownership).map(_.description).getOrElse("")
              )
            ),
            status = Some(
              AccountStatus(
                code = vendorResult.flatMap(_.status).map(_.code).getOrElse(""),
                description = vendorResult.flatMap(_.status).map(_.description).getOrElse(""),
                ewsDirectContributor = vendorResult.flatMap(_.status).map(_.ewsDirectContributor),
                mbLastSeen = vendorResult.flatMap(_.status).map(_.mbLastSeen),
                mbDecisionCode = vendorResult.flatMap(_.status).map(_.mbDecisionCode)
              )
            )
          )
        } else if (res.ownership.isDefined) {
          AIVendorResult(
            ownership = Some(
              AccountOwnership(
                code = vendorResult.flatMap(_.ownership).map(_.code).getOrElse(""),
                description = vendorResult.flatMap(_.ownership).map(_.description).getOrElse("")
              )
            ),
            status = None
          )
        } else if (res.status.isDefined) {
          AIVendorResult(
            ownership = None,
            status = Some(
              AccountStatus(
                code = vendorResult.flatMap(_.status).map(_.code).getOrElse(""),
                description = vendorResult.flatMap(_.status).map(_.description).getOrElse(""),
                ewsDirectContributor = Some(vendorResult.flatMap(_.status).map(_.ewsDirectContributor).getOrElse("N")),
                mbLastSeen = Some(vendorResult.flatMap(_.status).map(_.mbLastSeen).getOrElse("N")),
                mbDecisionCode = Some(vendorResult.flatMap(_.status).map(_.mbDecisionCode).getOrElse("N"))
              )
            )
          )
        } else {
          AIVendorResult(
            None,
            None)
        }
      case None => AIVendorResult(None, None)
    }

  }
}