package me.socure.ai.gateway.common.models.vendor

import me.socure.ai.gateway.grpc.resource.VendorRuleCodes

final case class AIVendorRuleCodes(
                                    numericalRulecodes: Map[String, Double],
                                    categoricalRulecodes: Map[String, String]
                                  ) {
  def toGrpc: VendorRuleCodes = {
    VendorRuleCodes.defaultInstance
      .withCategoricalRulecodes(categoricalRulecodes)
      .withNumericalRulecodes(numericalRulecodes)
  }
}

object AIVendorRuleCodes {
  def from(vendorRuleCodes: Option[VendorRuleCodes]): Option[AIVendorRuleCodes] = {
    vendorRuleCodes match {
      case Some(ruleCodes) =>
        Some(AIVendorRuleCodes(
          numericalRulecodes = ruleCodes.numericalRulecodes,
          categoricalRulecodes = ruleCodes.categoricalRulecodes
        ))
      case None => None
    }
  }
}
