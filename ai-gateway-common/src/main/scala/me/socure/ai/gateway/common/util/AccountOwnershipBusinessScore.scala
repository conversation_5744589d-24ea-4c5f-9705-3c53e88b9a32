package me.socure.ai.gateway.common.util

case class AccountOwnershipBusinessScore(aiBusName: String, aiEIN: String, aiPhone: String, aiAddress: String, aiAoAScore: Double) {
  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(aiBusName, aiEIN, aiPhone, aiAddress).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: AccountOwnershipBusinessScore => {
        obj.canEqual(this) &&
          aiBusName.toLowerCase() == obj.aiBusName.toLowerCase() &&
          aiEIN.toLowerCase() == obj.aiEIN.toLowerCase() &&
          aiAddress.toLowerCase() == obj.aiAddress.toLowerCase() &&
          aiPhone.toLowerCase() == obj.aiPhone.toLowerCase()
      }
      case _ => false
    }
  }

  def getAuditString(): String = {
    s" BusName $aiBusName, EIN: $aiEIN, Phone: $aiPhone, Address: $aiAddress, Score: $aiAoAScore"
  }
}
