package me.socure.ai.gateway.common.util

final case class AccountOwnershipScore(aiFirstName: String, aiLastName: String, aiSSN: String, aiDOB: String, aiPhone: String, aiAddress: String, aiAoAScore: Double, aiAoAScoreBand: Double) {
  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(aiFirstName, aiLastName, aiSSN, aiDOB, aiPhone, aiAddress).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: AccountOwnershipScore => {
        obj.canEqual(this) &&
          aiFirstName.toLowerCase() == obj.aiFirstName.toLowerCase() &&
          aiLastName.toLowerCase() == obj.aiLastName.toLowerCase() &&
          aiSSN.toLowerCase() == obj.aiSSN.toLowerCase() &&
          aiDOB.toLowerCase() == obj.aiDOB.toLowerCase() &&
          aiAddress.toLowerCase() == obj.aiAddress.toLowerCase() &&
          aiPhone.toLowerCase() == obj.aiPhone.toLowerCase()
      }
      case _ => false
    }
  }

  def getAuditString(): String = {
    s" FirstName $aiFirstName, LastName: $aiLastName, SSN: $aiSSN, DOB: $aiDOB , Phone: $aiPhone, Address: $aiAddress, Score Band: $aiAoAScoreBand, Score: $aiAoAScore"
  }

}
