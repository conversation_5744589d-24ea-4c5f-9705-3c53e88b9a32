package me.socure.ai.gateway.common.util
import me.socure.ai.gateway.common.models.Score

final case class AccountStatusScore(
                                     asvStatusCode: String,
                                     mbDecisionCode: String,
                                     mbLastSeen: String,
                                     ewsDirectContributor: String,
                                     score: Double
                                   ) extends Score {

  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(asvStatusCode, mbDecisionCode, mbLastSeen, ewsDirectContributor).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: AccountStatusScore => {
        obj.canEqual(this) &&
          this.asvStatusCode.toLowerCase() == obj.asvStatusCode.toLowerCase() &&
          this.mbDecisionCode.toLowerCase() == obj.mbDecisionCode.toLowerCase() &&
          this.mbLastSeen.toLowerCase() == obj.mbLastSeen.toLowerCase() &&
          this.ewsDirectContributor.toLowerCase() == obj.ewsDirectContributor.toLowerCase()
      }
      case _ => false
    }
  }

  def getAuditString(): String = {
    s"ASV Status Code $asvStatusCode, MBT Decision Code $mbDecisionCode, MBT Last Seen Thr $mbLastSeen, EWSDirectContributor $ewsDirectContributor, Score: $score"
  }
}

