package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}

import java.net.URL
import scala.collection.immutable

final case class AddressMatchLogic(consAddress: String, bnymAddress: String, saiAddressMatch: String) {
  override def hashCode(): Int = {
    List(consAddress, bnymAddress).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: AddressMatchLogic =>
        obj.canEqual(this) &&
          obj.consAddress.toLowerCase() == this.consAddress.toLowerCase() &&
          obj.bnymAddress.toLowerCase() == this.bnymAddress.toLowerCase()
      case _ => false
    }
  }
}

object AddressResolution {
  private val txnLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)
  val logicConfig: URL = getClass.getResource("/AddressMatchLogic.csv")
  implicit val dataDecoder: RowDecoder[AddressMatchLogic] = RowDecoder.ordered {
    (consAddress: String, bnymAddress: String, saiAddressMatch: String) => AddressMatchLogic(consAddress, bnymAddress, saiAddressMatch)
  }
  val rows: immutable.Seq[AddressMatchLogic] = logicConfig.asCsvReader[AddressMatchLogic](rfc.withHeader)
    .collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for address match ${x.getMessage}") }.toList
  private val addressMatches = rows.map { a => a -> a.saiAddressMatch }.toMap

  def addressMatch(consAddress: String, bnymAddress: String): String = {
    val key = AddressMatchLogic(consAddress, bnymAddress, "")
    addressMatches.getOrElse(key, ScoreHelper.UNKNOWN)
  }

  def getMatch(): scala.collection.immutable.Map[AddressMatchLogic, String] = addressMatches
} 