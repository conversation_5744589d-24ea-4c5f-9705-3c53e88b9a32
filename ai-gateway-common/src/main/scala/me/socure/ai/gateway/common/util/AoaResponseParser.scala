package me.socure.ai.gateway.common.util
import me.socure.ai.gateway.common.util.MatchStatus.MatchStatus

class AoaResponseParser extends ResponseParser {

  import AoaResponseParser._

  def getElements(description: String, matchCategory: String): Set[String] = {
    val reg = (matchCategory + "\\:[^;]+;").r
    val matches = reg.findFirstMatchIn(description)
    //    Will be None for both no matchCategory and UNKNOWN
    if (matches.isDefined) {
      val temp = (matchCategory + ": ").r.replaceAllIn(matches.get.toString, " ")
      val temp2 = ";".r.replaceAllIn(temp, " ")
      temp2.split(",").map(_.trim).toSet
    } else {
      Set.empty[String]
    }
  }

  def getMatchStatus(aoaStatusCode: String, aoaDescription: String): MatchStatus = {
    if (AOA_ERROR_CODE.equalsIgnoreCase(aoaStatusCode)) MatchStatus.ERROR
    else {
      aoaDescription match {
        case UNKNOWN_AOA_DESC => MatchStatus.UNKNOWN
        case _ =>
          val warningElements = getElements(aoaDescription, WARNING)
          val matchElements = getElements(aoaDescription, MATCH)
          val alertElements = getElements(aoaDescription, ALERT)
          val unknownElements = getElements(aoaDescription, UNKNOWN)

          /** FOR FNAME and LNAME * */
          val warnElements1: Set[String] = matchElements.size match {
            case 0 => warningElements - ("NAME")
            case _ => if (matchElements.contains("NAME")) warningElements - ("FNAME", "LNAME") else warningElements
          }

          /** For ADDRESS match logic * */
          val nonWarningElements = matchElements.union(alertElements)
          val isAddressInWarning = (!isAddressNone(warningElements ++ matchElements ++ alertElements ++ unknownElements)) &&
            (!(nonWarningElements.contains("ADDRESS") &&
              ((nonWarningElements.contains("CITY") && matchElements.contains("STATE")) ||
                nonWarningElements.contains("ZIP"))))

          val warnElements2: Set[String] = if (isAddressInWarning) warnElements1 + ("ADDRESS") else warnElements1
          val personNoMatch = warnElements2.intersect(PERSON_ELE)
          val contactNoMatch = warnElements2.intersect(CONTACT_ELE)

          warnElements2.size match {
            case 0 => if (isUnknown(aoaDescription)) MatchStatus.UNKNOWN else MatchStatus.MATCH
            case _ => if (personNoMatch.nonEmpty || contactNoMatch.size > 1) MatchStatus.NO_MATCH else MatchStatus.MATCH
          }
      }
    }
  }

  def isAddressNone(allElements: Set[String]): Boolean = {
    !allElements.contains("ADDRESS")
  }

  def isUnknown(aoaDescription: String): Boolean = {
    UNKNOWN_AOA_DESC.equalsIgnoreCase(aoaDescription)
  }

}


object AoaResponseParser {
  val PERSON_ELE = Set("FNAME", "LNAME", "SSN", "DOB", "BUSNAME")
  val CONTACT_ELE = Set("ADDRESS", "HMPHONE")
  val WARNING = "Warning"
  val MATCH = "Match"
  val ALERT = "Alert"
  val UNKNOWN = "Unknown"
  val AOA_ERROR_CODE = "ERROR"
  val UNKNOWN_AOA_DESC = "AOA data not available"
}
