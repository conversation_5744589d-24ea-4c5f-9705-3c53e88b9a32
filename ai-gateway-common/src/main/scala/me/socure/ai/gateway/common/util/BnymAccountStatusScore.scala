package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class BnymAccountStatusScore(
                                         bnymStatusCode: String,
                                         ewsDirectContributor: String,
                                         score: Double
                                       ) extends Score {

  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash; score is not used for lookup
    List(bnymStatusCode, ewsDirectContributor).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: BnymAccountStatusScore => {
        obj.canEqual(this) &&
          this.bnymStatusCode.toLowerCase() == obj.bnymStatusCode.toLowerCase() &&
          this.ewsDirectContributor.toLowerCase() == obj.ewsDirectContributor.toLowerCase()
      }
      case _ => false
    }
  }

  override def getAuditString(): String = {
    s"BNYM Status Code $bnymStatusCode, EWS Direct Contributor $ewsDirectContributor, Score: $score"
  }
}
