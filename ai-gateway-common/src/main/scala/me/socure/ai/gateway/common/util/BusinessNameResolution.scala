package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

case class BusinessNameResolution(bnyBusName: String, mbBusName: String, saiBusName: String) {
  override def hashCode(): Int = {
    List(bnyBusName, mbBusName).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: BusinessNameResolution => {
        obj.canEqual(this) &&
          obj.bnyBusName.toLowerCase() == this.bnyBusName.toLowerCase() &&
          obj.mbBusName.toLowerCase() == this.mbBusName.toLowerCase()
      }
      case _ => false
    }
  }
}

object BusinessNameResolution {
  val logicConfig = getClass.getResource("/BusinessNameResolutionLogic.csv")
  implicit val dataDecoder: RowDecoder[BusinessNameResolution] = RowDecoder.ordered {
    (bnyBusName: String, mbBusName: String, saiBusName: String) => BusinessNameResolution(bnyBusName, mbBusName, saiBusName)
  }
  val rows = logicConfig.asCsvReader[BusinessNameResolution](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for business name match ${x.getMessage}") }.toList
  val busNameMatches = rows.map { a => a -> a.saiBusName }.toMap

  def getMatch(): scala.collection.immutable.Map[BusinessNameResolution, String] = busNameMatches

  def busNameMatch(bnyBusName: String, mbBusName: String): String = {
    getMatch.getOrElse(BusinessNameResolution(bnyBusName, mbBusName, ""), "Unknown")
  }
}
