package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class ConvlStatusScore(
                                   convlStatus: String,
                                   score: Double
                                 ) extends Score {
  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(convlStatus).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: ConvlStatusScore => {
        obj.canEqual(this) &&
          this.convlStatus.toLowerCase() == obj.convlStatus.toLowerCase()
      }
      case _ => false
    }
  }

  def getAuditString(): String = {
    s"CONVL ASV STATUS $convlStatus, Score: $score"
  }
}
