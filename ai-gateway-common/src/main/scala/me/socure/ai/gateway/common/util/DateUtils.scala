package me.socure.ai.gateway.common.util

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.util.Try

object DateUtils {
  // Common date formatters for parsing various date formats
  private val formatters: Seq[DateTimeFormatter] = Seq(
    DateTimeFormatter.ofPattern("yyyy-MM-dd"),
    DateTimeFormatter.ofPattern("MM/dd/yyyy"),
    DateTimeFormatter.ofPattern("MM-dd-yyyy"),
    DateTimeFormatter.ofPattern("yyyyMMdd"),
    DateTimeFormatter.ofPattern("MMM dd, yyyy"),
    DateTimeFormatter.ofPattern("yyyy/MM/dd"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd H:mm:ss"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
    DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")
  )

  /**
   * Attempts to parse a date string using multiple formatters
   *
   * @param dateStr The date string to parse
   * @return Option containing the parsed LocalDate if successful, None otherwise
   */
  def parseDate(dateStr: String): Option[LocalDate] = {
    if (dateStr == null || dateStr.trim.isEmpty) {
      None
    } else {
      formatters.view
        .map(formatter => Try(LocalDate.parse(dateStr, formatter)).toOption)
        .collectFirst { case Some(date) => date }
    }
  }

  /**
   * Finds the maximum date from a sequence of dates
   *
   * @param dateList Sequence of LocalDate objects
   * @return Option containing the maximum date if the sequence is not empty, None otherwise
   */
  private def maxDate(dateList: Seq[LocalDate]): Option[LocalDate] = {
    if (dateList.isEmpty) None 
    else Some(dateList.max(Ordering.by[LocalDate, Long](_.toEpochDay)))
  }

  /**
   * Parses multiple date strings and returns the maximum date as a string
   *
   * @param dateStrings Sequence of date strings to parse
   * @param defaultValue Default value to return if no valid dates are found
   * @return String representation of the maximum date or the default value
   */
  def findMaxDateAsString(dateStrings: Seq[String], defaultValue: String = "UNKNOWN"): String = {
    val parsedDates = dateStrings.flatMap(parseDate)
    maxDate(parsedDates).map(_.toString).getOrElse(defaultValue)
  }
} 