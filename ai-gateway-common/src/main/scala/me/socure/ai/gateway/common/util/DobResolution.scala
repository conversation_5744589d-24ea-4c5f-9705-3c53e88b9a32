package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

import scala.collection.immutable

case class DobResolutionInput(consDobMatch: String, bnyDobMatch: String, socIdDobMatch: String, saiDobMatch: String) {
  override def hashCode(): Int = {
    List(consDobMatch, bnyDobMatch, socIdDobMatch).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: DobResolutionInput =>
        obj.canEqual(this) &&
          this.consDobMatch.toLowerCase() == obj.consDobMatch.toLowerCase() &&
          this.bnyDobMatch.toLowerCase() == obj.bnyDobMatch.toLowerCase() &&
          this.socIdDobMatch.toLowerCase() == obj.socIdDobMatch.toLowerCase()
      case _ => false
    }
  }
}

object DobResolution {
  private val dobLogicConfig = getClass.getResource("/DobMatchLogic.csv")

  implicit val fNameDataDecoder: RowDecoder[DobResolutionInput] = RowDecoder.ordered {
    (consDobMatch: String, bnyDobMatch: String, socIdDobMatch: String, saiDobMatch: String) =>
      DobResolutionInput(consDobMatch, bnyDobMatch, socIdDobMatch, saiDobMatch)
  }

  val rows: immutable.Seq[DobResolutionInput] = dobLogicConfig.asCsvReader[DobResolutionInput](rfc.withHeader)
    .collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for dob match ${x.getMessage}") }.toList
  private val dobMatches = rows.map { a => a -> a.saiDobMatch }.toMap

  def getMatch: Map[DobResolutionInput, String] = dobMatches

  def dobMatch(consDob: String, bnyDob: String, socIdDob: String): String = {
    getMatch.getOrElse(DobResolutionInput(consDob, bnyDob, socIdDob, ""), "Unknown")
  }
}
