package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

import scala.collection.immutable

case class FNameResolutionInput(consFirstNameMatch: String, bnyFirstNameMatch: String, mbFirstNameMatch: String, saiFirstNameMatch: String) {
  override def hashCode(): Int = {
    List(consFirstNameMatch, bnyFirstNameMatch, mbFirstNameMatch).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: FNameResolutionInput =>
        obj.canEqual(this) &&
          this.consFirstNameMatch.toLowerCase() == obj.consFirstNameMatch.toLowerCase() &&
          this.bnyFirstNameMatch.toLowerCase() == obj.bnyFirstNameMatch.toLowerCase() &&
          this.mbFirstNameMatch.toLowerCase() == obj.mbFirstNameMatch.toLowerCase()
      case _ => false
    }
  }
}

object FNameResolution {
  private val fNameLogicConfig = getClass.getResource("/FNameResolutionLogic.csv")

  implicit val fNameDataDecoder: RowDecoder[FNameResolutionInput] = RowDecoder.ordered {
    (consFirstNameMatch: String, bnyFirstNameMatch: String, mbFirstNameMatch: String, saiFirstNameMatch: String) =>
      FNameResolutionInput(consFirstNameMatch, bnyFirstNameMatch, mbFirstNameMatch, saiFirstNameMatch)
  }

  val rows: immutable.Seq[FNameResolutionInput] = fNameLogicConfig.asCsvReader[FNameResolutionInput](rfc.withHeader)
    .collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for name match ${x.getMessage}") }.toList
  val nameMatches: Map[FNameResolutionInput, String] = rows.map { a => a -> a.saiFirstNameMatch }.toMap

  def getMatch: Map[FNameResolutionInput, String] = nameMatches

  def nameMatch(consFirst: String, bnyFirst: String, mbFirst: String): String = {
    getMatch.getOrElse(FNameResolutionInput(consFirst, bnyFirst, mbFirst, ""), "Unknown")
  }
}
