package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class GlobalAccountStatusScore(
                                             asvTriceScore: Double,
                                             asvBnymScore: Double,
                                             asvMbltScore: Double,
                                             globalScore: Double
                                         ) extends Score {

    override def hashCode(): Int = {
        // This allows us to use this as a key in a Hash
        List(asvTriceScore, asvBnymScore, asvMbltScore).map(x => x).mkString("_").hashCode()
    }

    override def equals(obj: Any): Boolean = {
        obj match {
            case obj: GlobalAccountStatusScore => {
                obj.canEqual(this) &&
                    this.asvTriceScore == obj.asvTriceScore &&
                    this.asvBnymScore == obj.asvBnymScore &&
                    this.asvMbltScore == obj.asvMbltScore
            }
            case _ => false
        }
    }

    def getAuditString(): String = {
        s"ASV Trice Score $asvTriceScore,ASV BNYM Score $asvBnymScore,ASV MBLT Score $asvMbltScore, Score: $globalScore"
    }
}