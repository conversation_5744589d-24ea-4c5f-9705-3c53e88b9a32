package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class GlobalAccountStatusScoreWithConsortium(
                                                         asvTriceScore: Double,
                                                         asvBnymScore: Double,
                                                         asvConvlScore: Double,
                                                         asvMbltScore: Double,
                                                         globalScore: Double
                                                       ) extends Score {

  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(asvTriceScore, asvBnymScore, asvConvlScore, asvMbltScore).map(x => x).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: GlobalAccountStatusScoreWithConsortium => {
        obj.canEqual(this) &&
          this.asvTriceScore == obj.asvTriceScore &&
          this.asvBnymScore == obj.asvBnymScore &&
          this.asvConvlScore == obj.asvConvlScore &&
          this.asvMbltScore == obj.asvMbltScore
      }
      case _ => false
    }
  }

  def getAuditString(): String = {
    s"ASV Trice Score $asvTriceScore, ASV BNYM Score $asvBnymScore, ASV CONVL Score $asvConvlScore, ASV MBLT Score $asvMbltScore, Score: $globalScore"
  }
}
