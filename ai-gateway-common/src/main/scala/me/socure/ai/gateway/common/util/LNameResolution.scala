package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

import scala.collection.immutable

case class LNameResolutionInput(consLastNameMatch: String, bnyLastNameMatch: String, mbLastNameMatch: String, saiLastNameMatch: String) {
  override def hashCode(): Int = {
    List(consLastNameMatch, bnyLastNameMatch, mbLastNameMatch).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: LNameResolutionInput => {
        obj.canEqual(this) &&
          this.consLastNameMatch.toLowerCase() == obj.consLastNameMatch.toLowerCase() &&
          this.bnyLastNameMatch.toLowerCase() == obj.bnyLastNameMatch.toLowerCase() &&
          this.mbLastNameMatch.toLowerCase() == obj.mbLastNameMatch.toLowerCase()
      }
      case _ => false
    }
  }
}

object LNameResolution {
  private val lNameLogicConfig = getClass.getResource("/LNameResolutionLogic.csv")

  implicit val lNameDataDecoder: RowDecoder[LNameResolutionInput] = RowDecoder.ordered {
    (consLastNameMatch: String, bnyLastNameMatch: String, mbLastNameMatch: String, saiLastNameMatch: String) =>
      LNameResolutionInput(consLastNameMatch, bnyLastNameMatch, mbLastNameMatch, saiLastNameMatch)
  }

  val rows: immutable.Seq[LNameResolutionInput] = lNameLogicConfig.asCsvReader[LNameResolutionInput](rfc.withHeader)
    .collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for name match ${x.getMessage}") }.toList
  val nameMatches: Map[LNameResolutionInput, String] = rows.map { a => a -> a.saiLastNameMatch }.toMap

  def getMatch: Map[LNameResolutionInput, String] = nameMatches

  def nameMatch(consLast: String, bnyLast: String, mbLast: String): String = {
    getMatch.getOrElse(LNameResolutionInput(consLast, bnyLast, mbLast, ""), "Unknown")
  }
}
