package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class MbOnlyAccountStatusScore (
                                            mbDecisionCode: String,
                                            mbLastSeenMulti: String,
                                            mbReturns: String,
                                            score: Double
                                          ) extends Score {

  override def hashCode(): Int = {
    // This allows us to use this as a key in a Hash
    List(mbDecisionCode, mbLastSeenMulti, mbReturns).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: MbOnlyAccountStatusScore => {
        obj.canEqual(this) &&
          this.mbDecisionCode.toLowerCase() == obj.mbDecisionCode.toLowerCase() &&
          this.mbLastSeenMulti.toLowerCase() == obj.mbLastSeenMulti.toLowerCase() &&
          this.mbReturns.toLowerCase() == obj.mbReturns.toLowerCase()
      }
      case _ => false
    }
  }



  def getAuditString(): String = {
    s"MBT Decision Code $mbDecisionCode, MBT Last Seen Thr Multi $mbLastSeenMulti, MBT Returns Thr $mbReturns, Score: $score"
  }
}

