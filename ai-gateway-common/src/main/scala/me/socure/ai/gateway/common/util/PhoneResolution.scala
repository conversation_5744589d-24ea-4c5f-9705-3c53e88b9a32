package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

import scala.collection.immutable

case class PhoneResolutionInput(consPhoneMatch: String, bnyPhoneMatch: String, mbtPhoneMatch: String, saiPhoneMatch: String) {
  override def hashCode(): Int = {
    List(consPhoneMatch, bnyPhoneMatch, mbtPhoneMatch).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: PhoneResolutionInput =>
        obj.canEqual(this) &&
          this.consPhoneMatch.toLowerCase() == obj.consPhoneMatch.toLowerCase() &&
          this.bnyPhoneMatch.toLowerCase() == obj.bnyPhoneMatch.toLowerCase() &&
          this.mbtPhoneMatch.toLowerCase() == obj.mbtPhoneMatch.toLowerCase()
      case _ => false
    }
  }
}

object PhoneResolution {
  private val phoneLogicConfig = getClass.getResource("/PhoneMatchLogic.csv")

  implicit val fNameDataDecoder: RowDecoder[PhoneResolutionInput] = RowDecoder.ordered {
    (consPhoneMatch: String, bnyPhoneMatch: String, mbtPhoneMatch: String, saiPhoneMatch: String) =>
      PhoneResolutionInput(consPhoneMatch, bnyPhoneMatch, mbtPhoneMatch, saiPhoneMatch)
  }

  val rows: immutable.Seq[PhoneResolutionInput] = phoneLogicConfig.asCsvReader[PhoneResolutionInput](rfc.withHeader)
    .collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for phone match ${x.getMessage}") }.toList
  private val phoneMatches = rows.map { a => a -> a.saiPhoneMatch }.toMap

  private def getMatch: Map[PhoneResolutionInput, String] = phoneMatches

  def phoneMatch(consPhone: String, bnyPhone: String, mbtPhone: String): String = {
    getMatch.getOrElse(PhoneResolutionInput(consPhone, bnyPhone, mbtPhone, ""), "Unknown")
  }
}
