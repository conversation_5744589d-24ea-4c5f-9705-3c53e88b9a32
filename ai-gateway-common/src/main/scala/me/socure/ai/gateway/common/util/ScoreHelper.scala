package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}
import me.socure.ai.gateway.common.models.AIGatewayRequest
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.transaction.id.TrxId

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.collection.mutable
import scala.collection.mutable.Map
import scala.util.Try

case class OwnershipResult(
                            firstNameMatch: String,
                            lastNameMatch: String,
                            ssnMatch: String,
                            dobMatch: String,
                            socIdSsnMatch: String,
                            socIdDobMatch: String,
                            phoneMatch: String,
                            addressMatch: String,
                            aovPattern: String,
                            busNameMatch: String,
                            einMatch: String,
                            phoneMatchBus: String,
                            addressMatchBus: String,
                            maxLastSeenDate: String
                           )

case class AvailabilityResult(
                               bnymStatusCode: String,
                               mbDecision: String,
                               mbLastSeen: String,
                               mbReturns: String,
                               mbLastSeenMulti: String,
                               ewsDirectContributor: String,
                               asvPattern: String,
                               saiAccountStructureIssue: Boolean,
                               triceAsvDecision: String,
                               convlStatus: String,
                               maxLastSeenDate: String
                             )

case class MbOnlyAvailabilityResult(
                                     mbDecision: String,
                                     mbLastSeenMulti: String,
                                     mbReturns: String,
                                     asvPattern: String,
                                     saiAccountStructureIssue: Boolean,
                                     triceAsvDecision: String,
                                     convlStatus: String,
                                     maxLastSeenDate: String
                                   )

object ScoreHelper {
  private val txnLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val nameLogicKeys: Set[String] = Set("BNYVL.100016", "BNYVL.100017", "MBTVL.100001", "MBTVL.100002", "SOCVL.100001",
    "SOCVL.100002", "CONVL.101001", "CONVL.101002")
  private val phoneLogicKeys: Set[String] = Set("BNYVL.100022", "MBTVL.100003", "CONVL.101005")
  private val ssnLogicKeys: Set[String] = Set("BNYVL.100008", "SOCVL.100003", "CONVL.101003")
  val socIdNumericLogicKeys = Set("SOCVL.100005")
  private val dobLogicKeys: Set[String] = Set("BNYVL.100009", "SOCVL.100004", "CONVL.101004")
  val addressLogicKeys: Set[String] = Set("BNYVL.100018", "CONVL.101007")
  val businessLogicKeys: Set[String] = Set("BNYVL.100007")
  val logicKeys: Set[String] = nameLogicKeys ++ phoneLogicKeys ++ ssnLogicKeys ++ dobLogicKeys ++ addressLogicKeys ++ businessLogicKeys ++ ssnLogicKeys
  val numericLogicKeys: Set[String] = socIdNumericLogicKeys
  val socureIdScoreThreshold: Double = 0.7D

  val availabilityLogicKeys: Set[String] = Set("BNYVL.100001", "MBTVL.100004", "MBTVL.100005", "BNYVL.100020", "MBTVL.100009","MBTVL.100010","MBTVL.100011", "TRICE.100005", "CONVL.100002", "TRICE.100006", "BNYVL.100125", "MBTVL.100012")

  val WARNING = "Warning"
  val MATCH = "Match"
  val YES = "Yes"
  val NO_MATCH = "No Match"
  val CONDITIONAL = "Conditional"
  val ALERT = "Alert"
  val UNKNOWN = "Unknown"
  val NOT_PROVIDED = "Not Provided"
  val AOV_PATTERN_DEFAULT = "Unknown_Unknown_Unknown_Unknown_Unknown_Unknown"
  val VALID = "VALID"
  val INVALID = "INVALID"
  val TRICE_ASV_DECISION_ACCEPTED = "accepted"
  val ACCOUNT_STRUCTURE = "ACCOUNT STRUCTURE"

  val refMap = Map(MATCH -> "Yes",
    WARNING -> "No",
    UNKNOWN -> "Unknown",
    ALERT -> "Conditional",
    CONDITIONAL -> "Conditional",
    NO_MATCH -> "No"
  )

  case class UnknownBankData(rtn: String, accountPrefix: String)
  val rawData = getClass.getResource("/AccountStructureExclusionData.csv")
  implicit val dataDecoder2: RowDecoder[UnknownBankData] = RowDecoder.ordered {
    (routingNumber: String, accountPrefix: String) => UnknownBankData(routingNumber, accountPrefix)
  }
  val unknownBankData = rawData.asCsvReader[UnknownBankData](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process data for unknown banks ${x.getMessage}") }.toList
  def getUnknownBankData(): List[UnknownBankData] = unknownBankData

  private def fNameMatch(ruleCodeMap: Map[String, String]): String = {
    val mbtFirstNameMatch = ruleCodeMap.getOrElse("MBTVL.100001", UNKNOWN) match {
      case fNameMatch if "".equals(fNameMatch) => UNKNOWN
      case fNameMatch => fNameMatch
    }
    FNameResolution.nameMatch(
      ruleCodeMap.getOrElse("CONVL.101001", UNKNOWN),
      ruleCodeMap.getOrElse("BNYVL.100016", UNKNOWN),
      mbtFirstNameMatch
    )
  }

  private def lNameMatch(ruleCodeMap: Map[String, String]): String = {
    val mbtLastNameMatch = ruleCodeMap.getOrElse("MBTVL.100002",UNKNOWN) match {
      case lNameMatch if "".equals(lNameMatch) => UNKNOWN
      case lNameMatch => lNameMatch
    }
    LNameResolution.nameMatch(
      ruleCodeMap.getOrElse("CONVL.101002", UNKNOWN),
      ruleCodeMap.getOrElse("BNYVL.100017", UNKNOWN),
      mbtLastNameMatch
    )
  }

  private def isNameThresholdMatch(cRuleCodeMap: Map[String, String], nRuleCodeMap: Map[String, Double]): Boolean = {

    val saiFnameMatch = fNameMatch(cRuleCodeMap)
    val saiLnameMatch = lNameMatch(cRuleCodeMap)
    val socIdFnameFuzzy = cRuleCodeMap.getOrElse("SOCVL.100001", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val socIdLnameFuzzy = cRuleCodeMap.getOrElse("SOCVL.100002", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val socIdMatchScore: Double = nRuleCodeMap.getOrElse("SOCVL.100005", 0.0)

    val socIdFuzzyMatch = socIdFnameFuzzy.equals(MATCH) && socIdLnameFuzzy.equals(MATCH)
    val saiNameMatch = saiFnameMatch.equals(YES) && saiLnameMatch.equals(YES)
    val isOverThreshold = socIdMatchScore.>(socureIdScoreThreshold)

    isOverThreshold && socIdFuzzyMatch && saiNameMatch

  }

  private def socIdPiiMatch(cRuleCodeMap: Map[String, String], nRuleCodeMap: Map[String, Double], piiElement: String) : String = {

    val nameThresholdBool = isNameThresholdMatch(cRuleCodeMap, nRuleCodeMap)

    if(nameThresholdBool){
      refMap.getOrElse(piiElement, UNKNOWN)
    } else {
      UNKNOWN
    }
  }

  private def ssnMatch(cRuleCodeMap: Map[String, String], nRuleCodeMap: Map[String, Double]): String = {

    val ssn = cRuleCodeMap.getOrElse("SOCVL.100003", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val ssnResult = socIdPiiMatch(cRuleCodeMap, nRuleCodeMap, ssn)

    SsnResolution.ssnMatch(
      cRuleCodeMap.getOrElse("CONVL.101003", UNKNOWN),
      cRuleCodeMap.getOrElse("BNYVL.100008", UNKNOWN),
      ssnResult
    )
  }

  private def dobMatch(cRuleCodeMap: Map[String, String], nRuleCodeMap: Map[String, Double]): String = {

    val dob = cRuleCodeMap.getOrElse("SOCVL.100004", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val dobResult = socIdPiiMatch(cRuleCodeMap, nRuleCodeMap, dob)

    DobResolution.dobMatch(
      cRuleCodeMap.getOrElse("CONVL.101004", UNKNOWN),
      cRuleCodeMap.getOrElse("BNYVL.100009", UNKNOWN),
      dobResult
    )
  }

  private def einMatch(gatewayRequest: AIGatewayRequest, ruleCodeMap: Map[String, String]): String = {
    gatewayRequest.params.businessEin match {
      case None => NOT_PROVIDED
      case _ => refMap(ruleCodeMap.getOrElse("BNYVL.100008", UNKNOWN))
    }
  }

  private def busNameMatch(ruleCodeMap: Map[String, String]): String = {
    val mbtBusNameMatch = ruleCodeMap.getOrElse("MBTVL.100001", UNKNOWN) match {
      case busNameMatch if "".equals(busNameMatch) => UNKNOWN
      case busNameMatch => busNameMatch
    }
    BusinessNameResolution.busNameMatch(
      ruleCodeMap.getOrElse("BNYVL.100007", UNKNOWN),
      mbtBusNameMatch
    )
  }

  private def phoneMatch(ruleCodeMap: mutable.Map[String, String]): String = {
    val mbtBusNameMatch = ruleCodeMap.getOrElse("MBTVL.100003", UNKNOWN) match {
      case busNameMatch if "".equals(busNameMatch) => UNKNOWN
      case busNameMatch => busNameMatch
    }
    PhoneResolution.phoneMatch(
      ruleCodeMap.getOrElse("CONVL.101005", UNKNOWN),
      ruleCodeMap.getOrElse("BNYVL.100022", UNKNOWN),
      mbtBusNameMatch
    )
  }

  private def phoneMatchBus(gatewayRequest: AIGatewayRequest, cRuleCodeMap: Map[String, String]): String = {
    if (gatewayRequest.params.mobileNumber.isEmpty && gatewayRequest.params.businessPhone.isEmpty) {
      NOT_PROVIDED
    } else {
      phoneMatch(cRuleCodeMap)
    }
  }

  private def addressMatch(ruleCodeMap: mutable.Map[String, String]): String = {
    val consAddress = ruleCodeMap.getOrElse("CONVL.101007", UNKNOWN) match {
      case addressMatch if "".equals(addressMatch) => UNKNOWN
      case addressMatch => addressMatch
    }

    AddressResolution.addressMatch(
      consAddress,
      ruleCodeMap.getOrElse("BNYVL.100018", UNKNOWN)
    )
  }

  private def addressMatchBus(gatewayRequest: AIGatewayRequest, ruleCodeMap: Map[String, String]): String = {
    gatewayRequest.params.physicalAddress match {
      case None => NOT_PROVIDED
      case _ => addressMatch(ruleCodeMap)
    }
  }

  private def maxLastSeen(ruleCodeMap: mutable.Map[String, String]): String = {
    val dates = Seq(
      ruleCodeMap.getOrElse("TRICE.100006", ""),
      ruleCodeMap.getOrElse("MBTVL.100012", ""),
      ruleCodeMap.getOrElse("BNYVL.100125", "")
    )
    
    DateUtils.findMaxDateAsString(dates)
  }

  private def isAccountStructureIssue(gatewayRequest: AIGatewayRequest, ruleCodeMap: Map[String, String]): Boolean = {
    val bnyStatusCode: Option[String] = ruleCodeMap.get("BNYVL.100001")
    val propertyMessageSimp: Option[String] = ruleCodeMap.get("MBTVL.100009")
    val triceAsvDecision: Option[String] = ruleCodeMap.get("TRICE.100005")
    val accountStructureIssueExists = ACCOUNT_STRUCTURE.equalsIgnoreCase(propertyMessageSimp.getOrElse(""))
    val isBankDataUnknown = getUnknownBankData().exists(data => gatewayRequest.payment.routingNumber.equalsIgnoreCase(data.rtn) && gatewayRequest.payment.accountNumber.startsWith(data.accountPrefix))
    (isBankDataUnknown, bnyStatusCode, triceAsvDecision) match {
      case (true, _, _) => false
      case (_, bnyStatusCode, _) if VALID.equalsIgnoreCase(bnyStatusCode.getOrElse("")) || INVALID.equalsIgnoreCase(bnyStatusCode.getOrElse("")) => false
      case (_, _, triceAsvDecision) if TRICE_ASV_DECISION_ACCEPTED.equalsIgnoreCase(triceAsvDecision.getOrElse("")) => false
      case _ => accountStructureIssueExists
    }
//    getUnknownBankData().exists(data => (gatewayRequest.payment.routingNumber.equalsIgnoreCase(data.rtn) && gatewayRequest.payment.accountNumber.startsWith(data.accountPrefix))) match {
//      case false => (VALID.equalsIgnoreCase(bnyStatusCode.getOrElse("")) || INVALID.equalsIgnoreCase(bnyStatusCode.getOrElse(""))) && accountStructureIssueExists match {
//        case true => false //AS -> U && propertyMessageSimp -> None
//        case _ => accountStructureIssueExists //Nothing changes
//      }
//      case _ => false //AS -> U && propertyMessageSimp -> None
//    }
  }

  private def isMbAccountStructureIssue(gatewayRequest: AIGatewayRequest, ruleCodeMap: Map[String, String]): Boolean = {
    val propertyMessageSimp: Option[String] = ruleCodeMap.get("MBTVL.100009")
    val accountStructureIssueExists = ACCOUNT_STRUCTURE.equalsIgnoreCase(propertyMessageSimp.getOrElse(""))
    val triceAsvDecision: Option[String] = ruleCodeMap.get("TRICE.100005")
    val isBankDataUnknown = getUnknownBankData().exists(data => (gatewayRequest.payment.routingNumber.equalsIgnoreCase(data.rtn) && gatewayRequest.payment.accountNumber.startsWith(data.accountPrefix)))
    (isBankDataUnknown, triceAsvDecision) match {
      case (true, _ ) => false
      case (_, triceAsvDecision) if TRICE_ASV_DECISION_ACCEPTED.equalsIgnoreCase(triceAsvDecision.getOrElse("")) => false
      case _ => accountStructureIssueExists
    }
//    getUnknownBankData().exists(data => (gatewayRequest.payment.routingNumber.equalsIgnoreCase(data.rtn) && gatewayRequest.payment.accountNumber.startsWith(data.accountPrefix))) match {
//      case false =>  accountStructureIssueExists match {
//        case true => accountStructureIssueExists //Nothing changes
//        case _ => false //AS -> U && propertyMessageSimp -> None
//      }
//      case _ => false //AS -> U && propertyMessageSimp -> None
//    }
  }

  private def imputeStatusCodes(value: String): String = {
    value.replace("ERROR", "UNKNOWN")
  }

  def processRuleCodes(gatewayRequest: AIGatewayRequest, cRuleCodes: Map[String, String], nRuleCodes: Map[String, Double]): OwnershipResult = {
    implicit val trxId = TrxId(gatewayRequest.metadata.transactionId)
    gatewayRequest.params.businessName match {
      case None => processPersonalRuleCodes(cRuleCodes, nRuleCodes)
      case _ => processBusinessRuleCodes(gatewayRequest, cRuleCodes)
    }
  }

  def processPersonalRuleCodes( cRuleCodes: Map[String, String], nRuleCodes: Map[String, Double])(implicit trxId: TrxId): OwnershipResult = {
    val ssn = cRuleCodes.getOrElse("SOCVL.100003", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val dob = cRuleCodes.getOrElse("SOCVL.100004", UNKNOWN) match {
      case socIdMatch if "".equals(socIdMatch) => UNKNOWN
      case socIdMatch => socIdMatch
    }

    val saiFirstNameMatch = fNameMatch(cRuleCodes)
    val saiLastNameMatch = lNameMatch(cRuleCodes)
    val saiSsnMatch: String = ssnMatch(cRuleCodes, nRuleCodes)
    val saiDobMatch: String = dobMatch(cRuleCodes, nRuleCodes)
    val socIdSsn: String = socIdPiiMatch(cRuleCodes, nRuleCodes, ssn)
    val socIdDob: String = socIdPiiMatch(cRuleCodes, nRuleCodes, dob)
    val saiPhoneMatch: String = phoneMatch(cRuleCodes)
    val saiAddressMatch: String = addressMatch(cRuleCodes)
    val maxLastSeenDate: String = maxLastSeen(cRuleCodes)
    val aovPattern = List(OwnershipScorer.nameMatchScoringMap(saiFirstNameMatch), OwnershipScorer.nameMatchScoringMap(saiLastNameMatch), saiSsnMatch, saiDobMatch, saiPhoneMatch, saiAddressMatch).mkString("_")
    txnLogger.info(s"[ScorerHelper] Ownership Returns $saiFirstNameMatch, $saiLastNameMatch, $saiSsnMatch, $saiDobMatch, $saiPhoneMatch, $saiAddressMatch")
    OwnershipResult(saiFirstNameMatch, saiLastNameMatch, saiSsnMatch, saiDobMatch, socIdSsn, socIdDob, saiPhoneMatch, saiAddressMatch, aovPattern, UNKNOWN, UNKNOWN, UNKNOWN, UNKNOWN, maxLastSeenDate)
  }

  private def processBusinessRuleCodes(gatewayRequest: AIGatewayRequest, ruleCodes: Map[String, String])(implicit trxId: TrxId): OwnershipResult = {
    //Business requests specific variables
    val saiEinMatch: String = einMatch(gatewayRequest, ruleCodes)
    val saiBusNameMatch: String = busNameMatch(ruleCodes)
    val saiPhoneMatchBus: String = phoneMatchBus(gatewayRequest, ruleCodes)
    val saiAddressMatchBus: String = addressMatchBus(gatewayRequest, ruleCodes)
    val maxLastSeenDate: String = maxLastSeen(ruleCodes)
    val aovPattern = List(saiBusNameMatch, saiEinMatch, saiPhoneMatchBus, saiAddressMatchBus).mkString("_")
    txnLogger.info(s"[ScorerHelper] Business Ownership Returns $saiBusNameMatch, $saiEinMatch, $saiPhoneMatchBus, $saiAddressMatchBus")
    OwnershipResult(UNKNOWN, UNKNOWN, UNKNOWN, UNKNOWN, UNKNOWN, UNKNOWN, saiPhoneMatchBus, UNKNOWN, aovPattern,
      saiBusNameMatch, saiEinMatch, saiPhoneMatchBus, saiAddressMatchBus, maxLastSeenDate)
  }

  def processAvailabilityRuleCodes(gatewayRequest: AIGatewayRequest, ruleCodes: Map[String, String])(implicit trxId: TrxId): AvailabilityResult = {
    val accountStructureIssueExists = isAccountStructureIssue(gatewayRequest, ruleCodes)
    val bnymStatusCode = imputeStatusCodes(ruleCodes.getOrElse("BNYVL.100001", "UNKNOWN"))
    val mbDecision = ruleCodes("MBTVL.100004")
    val mbLastSeen = ruleCodes("MBTVL.100005")
    val mbReturns = ruleCodes("MBTVL.100010")
    val mbLastSeenMulti = ruleCodes("MBTVL.100011")
    val triceAsvDecision = ruleCodes.getOrElse("TRICE.100005", "unknown")
    val convlStatus = ruleCodes.getOrElse("CONVL.100002", "unknown")
    val ewsDirectContributor = ruleCodes("BNYVL.100020")
    val saiAccountStructureIssue = accountStructureIssueExists
    val asvPattern = List(bnymStatusCode, mbDecision, mbLastSeen, ewsDirectContributor).mkString("_")
    val maxLastSeenDate = maxLastSeen(ruleCodes)
    //CON-422: Overwrite account structure decision code for the score lookup
    val overrideMbDecision = !accountStructureIssueExists && mbDecision.equalsIgnoreCase("AS") match {
      case true => "U"
      case _ => mbDecision
    }
    txnLogger.info(s"[ScorerHelper] Availability Returns $bnymStatusCode, $mbDecision, $mbLastSeen, $ewsDirectContributor")
    AvailabilityResult(
      bnymStatusCode,
      overrideMbDecision,
      mbLastSeen,
      mbReturns,
      mbLastSeenMulti,
      ewsDirectContributor,
      asvPattern,
      saiAccountStructureIssue,
      triceAsvDecision,
      convlStatus,
      maxLastSeenDate
    )
  }

  // This function can potentially be part of a new ScoreHelper class, it is kept here in order to avoid duplication of ownership logic
  def processMbOnlyAvailabilityRuleCodes(gatewayRequest: AIGatewayRequest, ruleCodes: Map[String, String])(implicit trxId: TrxId): MbOnlyAvailabilityResult = {
    val mbDecision = ruleCodes("MBTVL.100004")
    val mbLastSeenMulti = ruleCodes("MBTVL.100011")
    val mbReturns = ruleCodes("MBTVL.100010")
    val triceAsvDecision = ruleCodes.getOrElse("TRICE.100005", "unknown")
    val convlStatus = ruleCodes.getOrElse("CONVL.100002", "unknown")
    val accountStructureIssue = isMbAccountStructureIssue(gatewayRequest, ruleCodes)
    val overrideMbDecision = !accountStructureIssue && mbDecision.equalsIgnoreCase("AS") match {
      case true => "U"
      case _ => mbDecision
    }
    val asvPattern = List(overrideMbDecision, mbLastSeenMulti, mbReturns).mkString("_")
    val maxLastSeenDate = maxLastSeen(ruleCodes)
    txnLogger.info(s"[ScorerHelper] Availability Returns for Mb Only $overrideMbDecision, $mbLastSeenMulti, $mbReturns")
    MbOnlyAvailabilityResult(
      overrideMbDecision,
      mbLastSeenMulti,
      mbReturns,
      asvPattern,
      accountStructureIssue,
      triceAsvDecision,
      convlStatus,
      maxLastSeenDate
    )
  }
}
