package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.transaction.id.TrxId
import org.slf4j.{Logger, LoggerFactory}


trait AIScorer {
  def getStatusScore(statusCode: String, mbDecisionCode: String, mbLastSeen: String, ewsDirectContributor: String)(implicit trxId: TrxId): Double

  def getMbOnlyAvailabilityScore(mbDecisonCode: String, mbLastSeenMulti: String, mbReturns: String)(implicit trxId: TrxId): Double

  def getOwnershipScore(matchResult: OwnershipResult, reqBusName: Option[String])(implicit trxId: TrxId): Double
}

class Scorer extends AIScorer {
  private val txnLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)

  def getStatusScore(statusCode: String, mbDecisionCode: String, mbLastSeen: String, ewsDirectContributor: String)(implicit trxId: TrxId): Double = {

    txnLogger.info(s"[AIScorer] Availability for SC ${statusCode.toLowerCase}, MBDC ${mbDecisionCode.toLowerCase}, MBLS ${mbLastSeen.toLowerCase}, EDC ${ewsDirectContributor.toLowerCase}")

    Scorer.getScores().getOrElse(AccountStatusScore(statusCode, mbDecisionCode, mbLastSeen, ewsDirectContributor, -1), -1)
  }

  def getMbOnlyAvailabilityScore(mbDecisonCode: String, mbLastSeenMulti: String, mbReturns: String)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for MBDC ${mbDecisonCode.toLowerCase}, MBLSM ${mbLastSeenMulti.toLowerCase}, MBRTNS ${mbReturns.toLowerCase}")
    MbOnlyStatusScorer.getScores().getOrElse(MbOnlyAccountStatusScore(mbDecisonCode, mbLastSeenMulti, mbReturns, -1), 0.68)
  }

  def getTriceAvailabilityScore(triceAsvDecision: String)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for TRICE_ASV_DECISION ${triceAsvDecision.toLowerCase}")
    TriceStatusScorer.getScores().getOrElse(TriceAccountStatusScore(triceAsvDecision, -1), 0.5)
  }

  def getBnymAvailabilityScore(bnymStatusCode: String, ewsDirectContributor: String)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for BNYM STATUS ${bnymStatusCode.toLowerCase} EWS_DIRECT_CONTRIBUTOR ${ewsDirectContributor}")
    BnymStatusScorer.getScores().getOrElse(BnymAccountStatusScore(bnymStatusCode, ewsDirectContributor, -1), 0.5)
  }

  def getConvlAvailabilityScore(convlStatus: String)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for CONS_ASV_STATUS ${convlStatus.toLowerCase}")
    ConvlStatusScorer.getScores().getOrElse(ConvlStatusScore(convlStatus, -1), 0.5)
  }


  def getGlobalAvailabilityScore(asvTriceScore: Double, asvBnymnScore: Double, asvMbltScore: Double)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for TRICE_SCORE $asvTriceScore BNYM_SCORE $asvBnymnScore MBLT_SCORE $asvMbltScore")
    GlobalStatusScorer.getScores().getOrElse(GlobalAccountStatusScore(asvTriceScore, asvBnymnScore, asvMbltScore, -1), -1)
  }

  def getGlobalAvailabilityScoreWithConsortium(asvTriceScore: Double, asvBnymnScore: Double, asvConvlScore: Double, asvMbltScore: Double)(implicit trxId: TrxId): Double = {
    txnLogger.info(s"[AIScorer] Availability for TRICE_SCORE $asvTriceScore BNYM_SCORE $asvBnymnScore CONVL_SCORE $asvConvlScore MBLT_SCORE $asvMbltScore")
    GlobalStatusScorerWithConsortium.getScores().getOrElse(GlobalAccountStatusScoreWithConsortium(asvTriceScore, asvBnymnScore, asvConvlScore, asvMbltScore, -1), -1)
  }

  def getOwnershipScore(matchResult: OwnershipResult, reqBusName: Option[String])(implicit trxId: TrxId): Double = {
    reqBusName match {
      case None =>
        txnLogger.info(s"[AIScorer] Ownership for FN ${matchResult.firstNameMatch.toLowerCase}, LN ${matchResult.lastNameMatch.toLowerCase}, SS ${matchResult.ssnMatch.toLowerCase}, DOB ${matchResult.dobMatch.toLowerCase}, PH ${matchResult.phoneMatch.toLowerCase}, AD ${matchResult.addressMatch.toLowerCase}")
        //map firstNameMatch & lastnameMatch (Conditional -> Yes)
        OwnershipScorer.getScores().getOrElse(
          AccountOwnershipScore(
            OwnershipScorer.nameMatchScoringMap(matchResult.firstNameMatch),
            OwnershipScorer.nameMatchScoringMap(matchResult.lastNameMatch),
            matchResult.ssnMatch,
            matchResult.dobMatch,
            matchResult.phoneMatch,
            matchResult.addressMatch,
            -1,
            -1
          ),
          -1
        )
      case _ =>
        txnLogger.info(s"[AIScorer] Ownership for BusName ${matchResult.busNameMatch.toLowerCase}, EIN ${matchResult.einMatch.toLowerCase}, PH ${matchResult.phoneMatchBus.toLowerCase}, AD ${matchResult.addressMatchBus.toLowerCase}")
        OwnershipBusinessScorer.getScores().getOrElse(
          AccountOwnershipBusinessScore(
            matchResult.busNameMatch,
            matchResult.einMatch,
            matchResult.phoneMatchBus,
            matchResult.addressMatchBus,
            -1
          ),
          -1
        )
    }
  }
}


object Scorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val scoreConfig = getClass.getResource("/AccountStatusScores.csv")
  implicit val dataDecoder: RowDecoder[AccountStatusScore] = RowDecoder.ordered {
    (asvStatusCode: String, mbDecisionCode: String, mbLastSeen: String, ewsDirectContributor: String, score: Double) =>
      AccountStatusScore(asvStatusCode, mbDecisionCode, mbLastSeen, ewsDirectContributor, score)
  }
  val rows = scoreConfig.asCsvReader[AccountStatusScore](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for status scores ${x.getMessage}") }.toList
  val statusScores = rows.map { a => a -> a.score }.toMap
  logger.info("Scorer object status scores")

  def getScores(): Map[AccountStatusScore, Double] = statusScores
}

object OwnershipScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val scoreConfig = getClass.getResource("/AccountOwnershipScores.csv")
  implicit val dataDecoder: RowDecoder[AccountOwnershipScore] = RowDecoder.ordered {
    (aiFirstName: String, aiLastName: String, aiSSN: String, aiDOB: String, aiPhone: String, aiAddress: String, aiAoAScore: Double, aiAoAScoreBand: Double) =>
      AccountOwnershipScore(aiFirstName, aiLastName, aiSSN, aiDOB, aiPhone, aiAddress, aiAoAScore, aiAoAScoreBand)
  }
  val rows = scoreConfig.asCsvReader[AccountOwnershipScore](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for ownership scores ${x.getMessage}") }.toList
  val ownershipScores = rows.map { a => a -> a.aiAoAScore }.toMap
  logger.info("Scorer object ownership scores")

  def getScores(): Map[AccountOwnershipScore, Double] = ownershipScores

  val nameMatchScoringMap = Map("Yes" -> "Yes",
    "No" -> "No",
    "Unknown" -> "Unknown",
    "Conditional" -> "Yes"
  )
}

object OwnershipBusinessScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val businessScoreConfig = getClass.getResource("/AccountOwnershipBusinessScores.csv")
  implicit val dataDecoder: RowDecoder[AccountOwnershipBusinessScore] = RowDecoder.ordered {
    (aiBusName: String, aiEIN: String, aiPhone: String, aiAddress: String, aiAoAScore: Double) =>
      AccountOwnershipBusinessScore(aiBusName, aiEIN, aiPhone, aiAddress, aiAoAScore)
  }
  val rows = businessScoreConfig.asCsvReader[AccountOwnershipBusinessScore](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for ownership scores ${x.getMessage}") }.toList
  val ownershipBusinessScores = rows.map { a => a -> a.aiAoAScore }.toMap
  logger.info("Scorer object ownership scores")

  def getScores(): Map[AccountOwnershipBusinessScore, Double] = ownershipBusinessScores
}

object MbOnlyStatusScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val mbOnlyStatusScoreConfig = getClass.getResource("/MbOnlyAccountStatusScores.csv")
  implicit val dataDecoder: RowDecoder[MbOnlyAccountStatusScore] = RowDecoder.ordered{
    (mbDecisonCode: String, mbLastSeenMulti: String, mbReturns: String, score: Double) =>
      MbOnlyAccountStatusScore(mbDecisonCode, mbLastSeenMulti, mbReturns, score)
  }

  val rows = mbOnlyStatusScoreConfig.asCsvReader[MbOnlyAccountStatusScore](rfc.withHeader).collect{ case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for mb only status scores ${x.getMessage}") }.toList
  val mbOnlyStatusScores = rows.map{a => a-> a.score}.toMap

  def getScores(): Map[MbOnlyAccountStatusScore, Double] = mbOnlyStatusScores
}

object TriceStatusScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val triceStatusScoreConfig = getClass.getResource("/TriceAccountStatusScores.csv")
  implicit val dataDecoder: RowDecoder[TriceAccountStatusScore] = RowDecoder.ordered{
    (asvDecision: String, score: Double) =>
      TriceAccountStatusScore(asvDecision, score)
  }

  val rows = triceStatusScoreConfig.asCsvReader[TriceAccountStatusScore](rfc.withHeader).collect{ case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for trice status scores ${x.getMessage}") }.toList
  val triceStatusScores = rows.map{a => a-> a.score}.toMap

  def getScores(): Map[TriceAccountStatusScore, Double] = triceStatusScores
}

object ConvlStatusScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  private val convlStatusScoreConfig = getClass.getResource("/ConvlAccountStatusScores.csv")
  implicit val dataDecoder: RowDecoder[ConvlStatusScore] = RowDecoder.ordered{
    (asvStatus: String, score: Double) =>
      ConvlStatusScore(asvStatus, score)
  }

  val rows = convlStatusScoreConfig.asCsvReader[ConvlStatusScore](rfc.withHeader).collect{ case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for convl status scores ${x.getMessage}") }.toList
  private val convlStatusScores = rows.map{ a => a-> a.score}.toMap

  def getScores(): Map[ConvlStatusScore, Double] = convlStatusScores
}

object BnymStatusScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val bnymStatusScoreConfig = getClass.getResource("/BnymAccountStatusScores.csv")
  implicit val dataDecoder: RowDecoder[BnymAccountStatusScore] = RowDecoder.ordered {
    (statusCode: String, ewsDirectContributor: String, score: Double) =>
      BnymAccountStatusScore(statusCode, ewsDirectContributor, score)
  }

  val rows = bnymStatusScoreConfig.asCsvReader[BnymAccountStatusScore](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for BNYM status scores ${x.getMessage}") }.toList
  val bnymStatusScores = rows.map { a => a -> a.score }.toMap

  def getScores(): Map[BnymAccountStatusScore, Double] = bnymStatusScores
}

object GlobalStatusScorer {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val glovalStatusScoreConfig = getClass.getResource("/AsvScoreUniversal.csv")
  implicit val dataDecoder: RowDecoder[GlobalAccountStatusScore] = RowDecoder.ordered{
    (asvTriceScore: Double, asvBnymScore: Double, asvMbltScore: Double, finalScore: Double) =>
      GlobalAccountStatusScore(asvTriceScore, asvBnymScore, asvMbltScore, finalScore)
  }

  val rows = glovalStatusScoreConfig.asCsvReader[GlobalAccountStatusScore](rfc.withHeader).collect{ case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for global status scores ${x.getMessage}") }.toList
  val globalStatusScores = rows.map{a => a-> a.globalScore}.toMap

  def getScores(): Map[GlobalAccountStatusScore, Double] = globalStatusScores
}

object GlobalStatusScorerWithConsortium {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val globalStatusScoreConfig = getClass.getResource("/AsvScoreUniversalWithConsortium.csv")
  implicit val dataDecoder: RowDecoder[GlobalAccountStatusScoreWithConsortium] = RowDecoder.ordered{
    (asvTriceScore: Double, asvBnymScore: Double, asvConvlScore: Double, asvMbltScore: Double, finalScore: Double) =>
      GlobalAccountStatusScoreWithConsortium(asvTriceScore, asvBnymScore, asvConvlScore, asvMbltScore, finalScore)
  }

  val rows = globalStatusScoreConfig.asCsvReader[GlobalAccountStatusScoreWithConsortium](rfc.withHeader).collect{ case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for global status scores ${x.getMessage}") }.toList
  val globalStatusScores = rows.map{a => a-> a.globalScore}.toMap

  def getScores(): Map[GlobalAccountStatusScoreWithConsortium, Double] = globalStatusScores
}
