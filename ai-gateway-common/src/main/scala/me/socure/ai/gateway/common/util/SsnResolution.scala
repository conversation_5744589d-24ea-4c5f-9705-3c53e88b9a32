package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{RowDecoder, rfc}

import scala.collection.immutable

case class SsnResolutionInput(consSsnMatch: String, bnySsnMatch: String, socIdSsnMatch: String, saiSsnMatch: String) {
  override def hashCode(): Int = {
    List(consSsnMatch, bnySsnMatch, socIdSsnMatch).map(x => x.toLowerCase()).mkString("_").hashCode()
  }

  override def equals(obj: Any): Boolean = {
    obj match {
      case obj: SsnResolutionInput =>
        obj.canEqual(this) &&
          this.consSsnMatch.toLowerCase() == obj.consSsnMatch.toLowerCase() &&
          this.bnySsnMatch.toLowerCase() == obj.bnySsnMatch.toLowerCase() &&
          this.socIdSsnMatch.toLowerCase() == obj.socIdSsnMatch.toLowerCase()
      case _ => false
    }
  }
}

object SsnResolution {
  private val ssnLogicConfig = getClass.getResource("/SsnMatchLogic.csv")

  implicit val fNameDataDecoder: RowDecoder[SsnResolutionInput] = RowDecoder.ordered {
    (consSsnMatch: String, bnySsnMatch: String, socIdSsnMatch: String, saiSsnMatch: String) =>
      SsnResolutionInput(consSsnMatch, bnySsnMatch, socIdSsnMatch, saiSsnMatch)
  }

  val rows: immutable.Seq[SsnResolutionInput] = ssnLogicConfig.asCsvReader[SsnResolutionInput](rfc.withHeader).collect { case Right(v) => v case Left(x) => throw new Exception(f"Unable to process config for ssn match ${x.getMessage}") }.toList
  private val ssnMatches = rows.map { a => a -> a.saiSsnMatch }.toMap

  def getMatch: Map[SsnResolutionInput, String] = ssnMatches

  def ssnMatch(consSsn: String, bnySsn: String, socIdSsn: String): String = {
    getMatch.getOrElse(SsnResolutionInput(consSsn, bnySsn, socIdSsn, ""), "Unknown")
  }
}
