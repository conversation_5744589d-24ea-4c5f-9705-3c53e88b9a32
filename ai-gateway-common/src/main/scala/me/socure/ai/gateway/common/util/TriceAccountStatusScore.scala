package me.socure.ai.gateway.common.util

import me.socure.ai.gateway.common.models.Score

final case class TriceAccountStatusScore(
                                            triceAsvDecision: String,
                                            score: Double
                                        ) extends Score {
    override def hashCode(): Int = {
        // This allows us to use this as a key in a Hash
        List(triceAsvDecision).map(x => x.toLowerCase()).mkString("_").hashCode()
    }

    override def equals(obj: Any): Bo<PERSON>an = {
        obj match {
            case obj: TriceAccountStatusScore => {
                obj.canEqual(this) &&
                    this.triceAsvDecision.toLowerCase() == obj.triceAsvDecision.toLowerCase()
            }
            case _ => false
        }
    }

    def getAuditString(): String = {
        s"Trice ASV Decision $triceAsvDecision, Score: $score"
    }
}
