package me.socure.ai.gateway.common.util.module

import com.google.inject.AbstractModule
import me.socure.ai.gateway.common.util.ResponseParser
import me.socure.ai.gateway.common.util.provider.ResponseParserProvider

class ResponseParserModule extends AbstractModule {

  override def configure(): Unit = {
    bind(classOf[ResponseParser]).toProvider(classOf[ResponseParserProvider]).asEagerSingleton()
  }
}
