package me.socure.ai.gateway.common.util.provider

import com.google.inject.Provider
import me.socure.ai.gateway.common.util.{AoaResponseParser, ResponseParser}

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class ResponseParserProvider @Inject()(implicit ec: ExecutionContext)

  extends Provider[ResponseParser] {

  override def get(): ResponseParser = {

    new AoaResponseParser
  }
}
