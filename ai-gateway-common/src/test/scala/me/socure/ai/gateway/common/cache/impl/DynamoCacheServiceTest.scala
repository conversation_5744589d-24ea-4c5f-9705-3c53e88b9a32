package me.socure.ai.gateway.common.cache.impl

import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers

import java.time.{LocalDateTime, ZoneId}

class DynamoCacheServiceTest extends AnyFunSuite with Matchers {
  test("isItemExpired should return true for current system time values since it will evaluate against system time afterwards") {
    val expected = true
    val actual = DynamoCacheService.isItemExpired(LocalDateTime.now().atZone(ZoneId.of("UTC")).toEpochSecond)

    System.out.println(s"Expected ${expected} v/s Actual ${actual}")
    actual should be(expected)
  }

  test("isItemExpired should return false for future dates") {
    val expected = false
    val actual = DynamoCacheService.isItemExpired(LocalDateTime.now().plusDays(1).atZone(ZoneId.of("UTC")).toEpochSecond)

    System.out.println(s"Expected ${expected} v/s Actual ${actual}")
    actual should be(expected)
  }

  test("validate expiry timestamp created") {
    val now = LocalDateTime.now()
    val hoursToAdd = 72

    val actual = DynamoCacheService.createExpiryHours(hoursToAdd)
    val expected = now.plusHours(hoursToAdd).atZone(ZoneId.of("UTC")).toEpochSecond.toString

    actual shouldBe(expected)
  }
}
