package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{HeaderDecoder, rfc}
import me.socure.common.transaction.id.TrxId
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks.forEvery
import org.scalatest.prop.TableFor2

import java.util.UUID

class AccountOwnerScoreTest extends AnyFlatSpec with should.Matchers {
  implicit val headerDecoder: HeaderDecoder[AOATestData] = HeaderDecoder.decoder("SAI_FNAME_MATCH", "SAI_LNAME_MATCH", "SAI_SSN_MATCH", "SAI_DOB_MATCH", "SAI_PHONE_MATCH", "SAI_ADDRESS_MATCH", "SAI_AOA_SCORE", "SAI_AOA_SCORE_BAND")(AOATestData)
  val rawData = getClass.getResource("/AccountOwnershipScoreTest.csv")
  val rows = rawData.asCsvReader[AOATestData](rfc.withHeader).collect { case Right(a) => a }.toList
  private implicit val trxId = TrxId(UUID.randomUUID().toString)

  var testCases = new TableFor2(("expected", "score"), (0.0, 0.0))
  val aoaScorer = new Scorer()
  rows.foreach(row => {
    val matchResult = OwnershipResult(row.aiFirstName, row.aiLastName, row.aiSSN, row.aiDOB, row.aiPhone, "", "", row.aiAddress, "", "", "", "", "", "")
    val result = aoaScorer.getOwnershipScore(matchResult, None)(TrxId("test"))
    testCases = testCases.++(new TableFor2(("result", "actual"), (result, row.aiAoAScore)))
  })
  forEvery(testCases) { (result, actual) =>
    assertResult(result) {
      actual
    }
  }

}
