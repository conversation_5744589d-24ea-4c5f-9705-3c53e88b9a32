package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{HeaderDecoder, rfc}
import me.socure.common.transaction.id.TrxId
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks.forEvery
import org.scalatest.prop.TableFor2

import java.util.UUID

class AccountOwnershipBusinessScoreTest extends AnyFlatSpec with should.Matchers {
  implicit val headerDecoder: HeaderDecoder[AOABusinessTestData] = HeaderDecoder.decoder("GLOBAL_SAI_BUSNAME_MATCH", "GLOBAL_SAI_EIN_MATCH", "GLOBAL_SAI_PHONE_MATCH", "GLOBAL_SAI_ADDRESS_MATCH", "GLOBAL_SAI_AOA_BUSINESS_SCORE")(AOABusinessTestData)
  val rawData = getClass.getResource("/AccountOwnershipBusinessScoreTest.csv")
  val rows = rawData.asCsvReader[AOABusinessTestData](rfc.withHeader).collect { case Right(a) => a }.toList
  private implicit val trxId = TrxId(UUID.randomUUID().toString)

  var testCases = new TableFor2(("expected", "score"), (0.0, 0.0))
  val aoaScorer = new Scorer()
  rows.foreach(row => {
    val matchResult = OwnershipResult("", "", "", "", "", "", "", "", "", row.saiBusName, row.saiEinMatch, row.saiPhoneMatch, row.saiAddressMatch, "")
    val result = aoaScorer.getOwnershipScore(matchResult, Some("Dummy"))(TrxId("test"))
    testCases = testCases.++(new TableFor2(("result", "actual"), (result, row.saiAoAScore)))
  })
  forEvery(testCases) { (result, actual) =>
    assertResult(result) {
      actual
    }
  }

}
