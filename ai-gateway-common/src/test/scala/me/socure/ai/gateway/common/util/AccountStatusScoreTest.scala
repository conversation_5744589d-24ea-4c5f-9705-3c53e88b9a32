package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{HeaderDecoder, rfc}
import me.socure.common.transaction.id.TrxId
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks.forEvery
import org.scalatest.prop.TableFor2

import java.util.UUID

class AccountStatusScoreTest extends AnyFlatSpec with should.Matchers {
  implicit val headerDecoder: HeaderDecoder[ASVTestData] = HeaderDecoder.decoder("BNYM_ASV_STATUS_CODE", "MBT_DECISION_CODE", "MBT_LAST_SEEN_THR", "BNYM_EWS_DIRECT_CONTRIBUTOR", "GLOBAL_SAI_ASV_SCORE")(ASVTestData)
  val rawData = getClass.getResource("/AccountStatusScoreTest.csv")
  private implicit val trxId = TrxId(UUID.randomUUID().toString)
  val rows = rawData.asCsvReader[ASVTestData](rfc.withHeader).collect {
    case Right(a) => a
  }.toList
  val scorer = new Scorer()
  var testCases = new TableFor2(("expected", "score"), (0.0, 0.0))
  rows.foreach(row => {
    val result = scorer.getStatusScore(row.statusCode, row.mbDecisionCode, row.mbLastSeen, row.ewsDirectContributor)(TrxId("test"))
    testCases = testCases.++(new TableFor2(("result", "actual"), (result, row.asvScore)))
  })
  forEvery(testCases) { (result, actual) =>
    assertResult(actual) {
      result
    }
  }
}
