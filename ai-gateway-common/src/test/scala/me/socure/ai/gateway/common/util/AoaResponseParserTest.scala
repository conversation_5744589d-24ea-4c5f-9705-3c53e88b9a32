package me.socure.ai.gateway.common.util

import kantan.csv.ops.toCsvInputOps
import kantan.csv.{HeaderDecoder, rfc}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.prop.TableDrivenPropertyChecks._
import org.scalatest.prop.TableFor3

class AoaResponseParserTest extends AnyFlatSpec with should.Matchers {

  implicit val headerDecoder: HeaderDecoder[TestData] = HeaderDecoder.decoder("id", "aoaStatusCode", "aoaDescription", "AIS_AOA_STATUS")(TestData)
  val rawData = getClass.getResource("/BNYMellonTestData.csv")
  val rows = rawData.asCsvReader[TestData](rfc.withHeader)
  val aoaResponseParser = new AoaResponseParser()

  val refMap = Map(MatchStatus.MATCH -> "Yes",
    MatchStatus.NO_MATCH -> "No",
    MatchStatus.UNKNOWN -> "Unknown",
    MatchStatus.ERROR -> ""
  )

  var testCases = new TableFor3(("id", "aoaDescription", "AIS_AOA_STATUS"), ("mock", "mock", "mock"))
  rows foreach { row =>
    val data = row.right.get
    val result = aoaResponseParser.getMatchStatus(data.aoaStatusCode, data.aoaDescription)
    testCases = testCases.++(new TableFor3(("id", "aoaDescription", "AIS_AOA_STATUS"), (data.id, refMap.get(result).getOrElse(""), data.aisAoaStatus.getOrElse(""))))
  }

  forEvery(testCases) { (id, input, expected) =>
    assertResult(expected) {
      input
    }
  }
}
