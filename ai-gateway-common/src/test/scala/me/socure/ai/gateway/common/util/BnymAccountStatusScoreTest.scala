package me.socure.ai.gateway.common.util

import me.socure.common.transaction.id.TrxId
import org.scalatest.freespec.AnyFreeSpec
import org.scalatest.matchers.should.Matchers

class BnymAccountStatusScoreTest extends AnyFreeSpec with Matchers {
  val Valid = "VALID"
  val Unknown = "UNKNOWN"
  val Invalid = "INVALID"
  val Y = "Y"
  val N = "N"

  "BNYM Account Score Test Scenarios:" - {
    implicit val trxId = TrxId("bnym-test-transaction")
    val scorer = new Scorer
    //BNYM ASV Status Code, BNYM EWS Direct Contributor, Score (Expected)
    val TestCases = List(
      (Some(Valid), Some(Y), 0.95),
      (Some(Valid), Some(N), 0.93),
      (Some(Unknown), Some(Y), 0.4),
      (Some(Unknown), Some(N), 0.68),
      (Some(Invalid), Some(Y), 0.09),
      (Some(Invalid), Some(N), 0.1),
      (Some("invalid"), Some("n"), 0.1),  //Upper case or lower case doesn't matter
      (None, None, 0.5),  //Default Case
      (None, Some(Y), 0.5)  //Default Case
    )


    TestCases.foreach(testCase =>
      s"when BNYM ASV Status Code is ${testCase._1} and EWS Direct Contributor is ${testCase._2} then score should be ${testCase._3}" in {
        scorer.getBnymAvailabilityScore(testCase._1.getOrElse(""), testCase._2.getOrElse("")) shouldBe testCase._3
      }
    )
  }
}
