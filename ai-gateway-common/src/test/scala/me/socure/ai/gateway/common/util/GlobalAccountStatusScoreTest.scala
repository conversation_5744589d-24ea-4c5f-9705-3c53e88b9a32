package me.socure.ai.gateway.common.util

import me.socure.common.transaction.id.TrxId
import org.scalatest.freespec.AnyFreeSpec
import org.scalatest.matchers.should.Matchers

class GlobalAccountStatusScoreTest extends AnyFreeSpec with Matchers {

  "Global Account Score Test Scenarios:" - {
    implicit val trxId = TrxId("global-test-transaction")
    val scorer = new Scorer
    //TRICE ASV Score, BNYM ASV Score, CONVL ASV Score, MBLT ASV Score, Score (Expected)
    val TestCases = List(
      (0.01,0.09,0.5,0.02,0.04),
      (0.98,0.09,0.99,0.23,0.7),
      (0.5,0.5,0.68,0.4, -1)  //Default Case
    )


    TestCases.foreach(testCase =>
      s"when score: TRICE ${testCase._1}, BNYM ${testCase._2}, CONVL ${testCase._3}, MBLT ${testCase._4} then score should be ${testCase._5}" in {
        scorer.getGlobalAvailabilityScoreWithConsortium(testCase._1, testCase._2, testCase._3, testCase._4) shouldBe testCase._5
      }
    )
  }
}
