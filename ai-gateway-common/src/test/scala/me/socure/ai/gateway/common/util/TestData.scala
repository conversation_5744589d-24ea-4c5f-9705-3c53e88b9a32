package me.socure.ai.gateway.common.util

case class TestData(
                     id: String,
                     aoaStatusCode: String,
                     aoaDescription: String,
                     aisAoaStatus: Option[String]
                   )

case class ASVTestData(
                        statusCode: String,
                        mbDecisionCode: String,
                        mbLastSeen: String,
                        ewsDirectContributor: String,
                        asvScore: Double
                      )

case class AOATestData(
                        aiFirstName: String,
                        aiLastName: String,
                        aiSSN: String,
                        aiDOB: String,
                        aiPhone: String,
                        aiAddress: String,
                        aiAoAScore: Double,
                        aiAoAScoreBand: Double
                      )

case class AOABusinessTestData(
                        saiBusName: String,
                        saiEinMatch: String,
                        saiPhoneMatch: String,
                        saiAddressMatch: String,
                        saiAoAScore: Double
                      )

case class ScoreTestData(
                          id: String,
                          BNYVL_100001: String,
                          BNYVL_100016: String,
                          BNYVL_100017: String,
                          BNYVL_100018: String,
                          BNYVL_100022: String,
                          BNYVL_100008: String,
                          BNYVL_100009: String,
                          BNYVL_100020: String,
                          MBTVL_100001: String,
                          MBTVL_100002: String,
                          MBTVL_100003: String,
                          MBTVL_100004: String,
                          MBTVL_100005: String,
                          MBTVL_100009: String,
                          GLOBAL_SAI_ASV_SCORE: String,
                          GLOBAL_SAI_AOV_SCORE: String,
                          GLOBAL_SAI_ASV_PATTERN: String,
                          GLOBAL_SAI_AOV_PATTERN: String,
                          accountNumber: String,
                          routingNumber: String
                        ) {
  def toMap: scala.collection.mutable.Map[String, String] = {
    scala.collection.mutable.Map[String, String](
      "id" -> id,
      "BNYVL.100001" -> BNYVL_100001,
      "BNYVL.100016" -> BNYVL_100016,
      "BNYVL.100017" -> BNYVL_100017,
      "BNYVL.100018" -> BNYVL_100018,
      "BNYVL.100022" -> BNYVL_100022,
      "BNYVL.100008" -> BNYVL_100008,
      "BNYVL.100009" -> BNYVL_100009,
      "BNYVL.100020" -> BNYVL_100020,
      "MBTVL.100001" -> MBTVL_100001,
      "MBTVL.100002" -> MBTVL_100002,
      "MBTVL.100003" -> MBTVL_100003,
      "MBTVL.100004" -> MBTVL_100004,
      "MBTVL.100005" -> MBTVL_100005,
      "MBTVL.100009" -> MBTVL_100009,
      "GLOBAL_SAI_ASV_SCORE" -> GLOBAL_SAI_ASV_SCORE,
      "GLOBAL_SAI_AOV_SCORE" -> GLOBAL_SAI_AOV_SCORE,
      "GLOBAL_SAI_ASV_PATTERN" -> GLOBAL_SAI_ASV_PATTERN,
      "GLOBAL_SAI_AOV_PATTERN" -> GLOBAL_SAI_AOV_PATTERN
    )
  }
}

case class MbOnlyScoreTestData(
                          id: String,
                          MBTVL_100004: String,
                          MBTVL_100011: String,
                          MBTVL_100010: String,
                          BNYVL_100001: String,
                          MBTVL_100009: String,
                          GLOBAL_SAI_ASV_PATTERN: String,
                          GLOBAL_SAI_ASV_SCORE: String,
                          accountNumber: String,
                          routingNumber: String
                          ) {
  def toMap: scala.collection.mutable.Map[String, String] = {
    scala.collection.mutable.Map[String, String](
      "id" -> id,
      "MBTVL.100004" -> MBTVL_100004,
      "MBTVL.100011" -> MBTVL_100011,
      "MBTVL.100010" -> MBTVL_100010,
      "BNYVL.100001" -> BNYVL_100001,
      "MBTVL.100009" -> MBTVL_100009,
      "GLOBAL_SAI_ASV_PATTERN" -> GLOBAL_SAI_ASV_PATTERN,
      "GLOBAL_SAI_ASV_SCORE" -> GLOBAL_SAI_ASV_SCORE
    )
  }

}


case class BusinessScoreTestData(
                          id: String,
                          BNYVL_100018: String, //BNYM_ADDRESS_MATCH
                          BNYVL_100022: String, //BNYM_PHONE
                          BNYVL_100008: String, //BNYM_SSN
                          BNYVL_100007: String, //BNYM_BUSNAME
                          MBTVL_100001: String, //MBT_FNAME_MATCH
                          MBTVL_100003: String, //MBT_PHONE_MATCH
                          GLOBAL_SAI_AOA_BUSINESS_SCORE: String,
                          businessName: Option[String] = None,
                          businessPhone: Option[String] = None,
                          physicalAddress: Option[String] = None
                        ) {
  def toMap: scala.collection.mutable.Map[String, String] = {
    scala.collection.mutable.Map[String, String](
      "id" -> id,
      "BNYVL.100018" -> BNYVL_100018,
      "BNYVL.100022" -> BNYVL_100022,
      "BNYVL.100008" -> BNYVL_100008,
      "BNYVL.100007" -> BNYVL_100007,
      "MBTVL.100001" -> MBTVL_100001,
      "MBTVL.100003" -> MBTVL_100003,
      "GLOBAL_SAI_AOA_BUSINESS_SCORE" -> GLOBAL_SAI_AOA_BUSINESS_SCORE
    )
  }
}