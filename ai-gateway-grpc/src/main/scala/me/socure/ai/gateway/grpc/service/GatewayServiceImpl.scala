package me.socure.ai.gateway.grpc.service

import io.grpc.Status
import me.socure.ai.gateway.common.models._
import me.socure.ai.gateway.grpc.resource._
import me.socure.ai.gateway.vendor.service.AIGatewayVendorService
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.transaction.id.TrxId

import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class GatewayServiceImpl(aiGatewayVendorService: AIGatewayVendorService)
                        (implicit ec: ExecutionContext) extends GatewayServiceGrpc.GatewayService {

  import GatewayServiceImpl._

  override def getAccountIntelligence(request: GatewayRequest): Future[GatewayResponse] = {
    implicit val trxId: TrxId = TrxId(request.metadata.map(_.transactionId).getOrElse("Unknown"))
    logger.info(s"Received getAccountIntelligence request")
    val response = aiGatewayVendorService.processGrpc(AIGatewayRequest.from(request))
    response.map {
      case Right(response) =>
        logger.info(s"getAccountIntelligence request succeeded")
        logger.info(response.toString)
        response
      case Left(error) =>
        logger.error(s"getAccountIntelligence request failed for accountId ${request.metadata.get.accountId} with error ${error.message}")
        throw new Exception(error.message)
    }.recoverWith {
      case NonFatal(ex) =>
        logger.error("Exception in getAccountIntelligence", ex)
        Future.failed(ex)
    }
  }

  override def healthCheck(request: Empty): Future[HealthCheckResponse] = {
    Future(HealthCheckResponse("Success"))
  }
}

object GatewayServiceImpl {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)

}
