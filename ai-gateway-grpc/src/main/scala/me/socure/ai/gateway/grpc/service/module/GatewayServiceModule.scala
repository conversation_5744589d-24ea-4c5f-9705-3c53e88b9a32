package me.socure.ai.gateway.grpc.service.module

import com.google.inject.{AbstractModule, Provides, Singleton}
import me.socure.ai.gateway.grpc.service.GatewayServiceGrpc.GatewayService
import me.socure.ai.gateway.grpc.service.GatewayServiceImpl
import me.socure.ai.gateway.vendor.service.AIGatewayVendorService

import scala.concurrent.ExecutionContext

class GatewayServiceModule extends AbstractModule {

  @Provides
  @Singleton
  def get(aiGatewayVendorService: AIGatewayVendorService)(implicit ec: ExecutionContext): GatewayService =
    new GatewayServiceImpl(aiGatewayVendorService)

}
