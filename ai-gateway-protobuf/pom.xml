<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-gateway</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ai-gateway-protobuf</artifactId>
    <version>${revision}</version>
    <properties>
        <scalapb.version>0.9.8</scalapb.version>
        <socure.commons.version>${revision}</socure.commons.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.thesamet.scalapb</groupId>
            <artifactId>scalapb-runtime-grpc_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thesamet.scalapb</groupId>
            <artifactId>scalapb-json4s_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-hmac-http</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
    </dependencies>
    <build>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.6.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>3.3.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                        <configuration>
                            <args>
                                <arg>-dependencyfile</arg>
                                <arg>${project.build.directory}/.scala_dependencies</arg>
                            </args>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <!-- Tests will be run with scalatest-maven-plugin instead -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!--<plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <version>2.0.0</version>
                <configuration>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                    <junitxml>.</junitxml>
                    <filereports>TestSuiteReport.txt</filereports>
                    &lt;!&ndash; Comma separated list of JUnit test class names to execute &ndash;&gt;
                    <jUnitClasses>scalapb.demo.ProtoSuite</jUnitClasses>
                </configuration>
                <executions>
                    <execution>
                        <id>test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>-->
            <!-- Add protobuf-maven-plugin and provide ScalaPB as a code generation plugin -->
            <plugin>
                <groupId>com.github.os72</groupId>
                <artifactId>protoc-jar-maven-plugin</artifactId>
                <version>3.11.4</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <protocArtifact>com.google.protobuf:protoc:3.17.3</protocArtifact>
                    <includeMavenTypes>transitive</includeMavenTypes>
                    <outputTargets>
                        <outputTarget>
                            <type>scalapb</type>
                            <outputOptions>grpc</outputOptions>
                            <!-- more scalapb options can be added here -->
                            <pluginArtifact>com.thesamet.scalapb:protoc-gen-scala:0.9.6:sh:unix</pluginArtifact>
                        </outputTarget>
                    </outputTargets>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
