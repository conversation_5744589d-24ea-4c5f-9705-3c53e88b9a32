syntax = "proto3";

package me.socure.ai.gateway.grpc;

option java_multiple_files = true;

/*
  Gateway service request and response messages
 */

message MetaData {
  int64 accountId = 1;
  int32 environmentTypeId = 2;
  bool maskPii = 3;
  string transactionId = 4;
  string memo = 5;
  string ultimateSendingPartyId = 6;
  int64 submissionDate = 7;
}

message Params {
  string firstName = 1;
  string surName = 2;
  string businessName = 3;
  string physicalAddress = 4;
  string physicalAddress2 = 5;
  string city = 6;
  string state = 7;
  string zip = 8;
  string country = 9;
  string nationalId = 10;
  string mobileNumber = 11;
  string dob = 12;
  string businessEin = 13;
  string email = 14;
}

message Payment {
  string accountNumber = 1;
  string routingNumber = 2;
  repeated string inquiries = 3;
  string accountCountry = 4;
}

message VendorConfig {
  string name = 1;
  int64 timeoutInMillis = 2;
}

message GatewayRequest {
  MetaData metadata = 1;
  Params params = 2;
  Payment payment = 3;
  repeated VendorConfig vendors = 4;
}

message GlobalRulecodes {
  string name = 1;
  map<string, double> numericalRulecodes = 2;
  map<string, string> categoricalRulecodes = 3;
}

message GatewayScoreBreakdown {
 double mbltScore = 1;
 double bnymScore = 2;
 double triceScore = 3;
}

message GatewayScore {
 double availabilityScore = 1;
 double ownershipScore = 2;
 GatewayScoreBreakdown availabilityScoreBreakdown = 3;
 GatewayScoreBreakdown ownershipScoreBreakdown = 4;
}

message GatewayResponse {
  string status = 1;
  repeated VendorResponse vendorResponses = 2;
  GlobalRulecodes globalRulecodes = 3;
  repeated string reasonCodes = 4;
  GatewayScore score = 5;
}

/*
  Healthcheck messages
 */

message Empty {}

message HealthCheckResponse {
  string message = 1;
}


/*
  Section for vendor services messages (request and response)
 */

message VendorRequest {
  int64 accountId = 1;
  int32 environmentTypeId = 2;
  bool maskPii = 3;
  string transactionId = 4;
  string accountNumber = 5;
  string routingNumber = 6;
  repeated string inquiries = 7;
  string firstName = 8;
  string surName = 9;
  string businessName = 10;
  string physicalAddress = 11;
  string physicalAddress2 = 12;
  string acceptanceCriteria = 13;
  string city = 14;
  string state = 15;
  string zip = 16;
  string nationalId = 17;
  string mobileNumber = 18;
  string dob = 19;
  string country = 20;
  string ein = 21;
  string email = 22;
  string memo = 23;
  string ultimateSendingPartyId = 24;
  string accountCountry = 25;
  int64 submissionDate = 26;
}

enum Name {
  BNYMELLON = 0;
  MICROBILT = 1;
  SOCUREID = 2;
  TRICE = 3;
  CONVL = 4;
  VRVAL = 5;
}

message VendorMetadata {
  Name name = 1;
  bool success = 2;
  string statusCode = 3;
  string vendorReferenceId = 4;
  string transactionId = 5;
}

message AccountOwnership {
  string code = 1;
  string description = 2;
}

message AccountStatus {
  string code = 1;
  string description = 2;
  string mbDecisionCode = 3;
  string mbLastSeen = 4;
  string ewsDirectContributor = 5;
}

message Result {
  AccountOwnership ownership = 1;
  AccountStatus status = 2;
  string ewsDirectContributor = 3;
}

message VendorError {
  string code = 1;
  string description = 2;
}

message VendorRuleCodes {
  map<string, double> numericalRulecodes = 1;
  map<string, string> categoricalRulecodes = 2;
}

message VendorResponse {
  VendorMetadata metadata = 1;
  Result result =  2;
  VendorError vendorError = 3;
  VendorRuleCodes rulecodes = 4;
}