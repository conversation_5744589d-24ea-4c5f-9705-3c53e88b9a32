syntax = "proto3";

package me.socure.ai.gateway.grpc;

import "resource.proto";

option java_multiple_files = true;

service GatewayService {
  rpc getAccountIntelligence(GatewayRequest) returns (GatewayResponse);
  rpc HealthCheck(Empty) returns (HealthCheckResponse);
}

//Common vendor gRPC service definitions
service AiCommonVendorService {
  rpc ValidateAccount(VendorRequest) returns (VendorResponse);
  rpc HealthCheck(Empty) returns (HealthCheckResponse);
}