package me.socure.ai.gateway.reasoncode

import me.socure.ai.gateway.reasoncode.models.ApplicationJson
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.retry.Retry
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.scheduler.DynamicValue
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.reasoncode.client.ReasonCodeServiceClient

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}

class ReasonCodeResolverImpl(reasonCodeServiceClient: ReasonCodeServiceClient,
                             cacheRefreshIntervalInMinutes: Int = 15,
                             retryStrategy: RetryStrategy
                            )(implicit ec: ExecutionContext) extends ReasonCodeResolver {

  import ReasonCodeResolverImpl._

  val appJsonCache: DynamicValue[ApplicationJson] = DynamicValue(
    provider = () => {
      Retry.apply(() => getApplicationJson)(retryStrategy)
    },
    refreshInterval = FiniteDuration(cacheRefreshIntervalInMinutes, TimeUnit.MINUTES),
    initialValueTimeout = cacheInitialValueTimeout
  )

  private def getApplicationJson: Future[ApplicationJson] = {
    implicit val trxId = TrxId("APP-JSON-FETCH")
    metrics.timeFuture("appjson_get_duration") {
      reasonCodeServiceClient.getApplicationJson(reasonCodesVersion)
    }.map {
      case Right(jsonStr) =>
        logger.info("Successfully fetched application json from reasoncode service")
        ApplicationJson.parse(jsonStr, moduleName)
      case Left(error) =>
        metrics.increment("appjson_fetch_error", s"code:${error.code}", s"message:${error.message}")
        logger.error(s"Error while fetching application json from reasoncode service ${error.code} ${error.message}")
        ApplicationJson.empty
    }.recover {
      case ex: Exception =>
        metrics.increment("appjson_fetch_error", s"ex_class:${ex.getClass.getSimpleName}", s"ex_message:${ex.getMessage}")
        logger.error("Exception while fetching application json from reasoncode service", ex)
        throw ex
    }
  }

  override def resolve(globalNumericRulecodes: Map[String, Double])(implicit trxId: TrxId): Future[Either[ErrorResponse, Set[String]]] = Future {

    val rulecodesEligible = globalNumericRulecodes.filter(rc => rc._2 > 0).keySet
    if (rulecodesEligible.isEmpty) Right(Set.empty[String])
    else {
      if (appJsonCache.getCurrentValue.data.isEmpty) {
        logger.error(applicationJsonFetchError.message)
        Left(applicationJsonFetchError)
      } else {
        val reasonCodes = rulecodesEligible.foldLeft(Set.empty[String]) { case (overall, current) =>
          if (appJsonCache.getCurrentValue.data.contains(current.replace("GLOBAL.", ""))) {
            overall + appJsonCache.getCurrentValue.data.getOrElse(current.replace("GLOBAL.", ""), "")
          } else overall
        }.filter(_.nonEmpty)
        Right(reasonCodes)
      }
    }
  }.recover {
    case ex: Exception =>
      logger.error("Exception while computing reasoncodes", ex)
      Left(reasonCodeComputationError)
  }
}

object ReasonCodeResolverImpl {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)

  private val reasonCodesVersion = "v3.0"
  private val cacheInitialValueTimeout = 45.seconds

  private val applicationJsonFetchError = ErrorResponse(500, "Error fetching application json data")
  private val reasonCodeComputationError = ErrorResponse(500, "Reasoncode computation failed")
  private val moduleName = "accountintelligence"

  def apply(reasonCodeServiceClient: ReasonCodeServiceClient,
            cacheRefreshIntervalInMinutes: Int,
            retryStrategy: RetryStrategy
           )(implicit ec: ExecutionContext): ReasonCodeResolverImpl = {
    new ReasonCodeResolverImpl(reasonCodeServiceClient, cacheRefreshIntervalInMinutes, retryStrategy)
  }
}