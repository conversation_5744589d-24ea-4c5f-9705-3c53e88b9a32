package me.socure.ai.gateway.reasoncode.models

import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats}
import org.slf4j.{Logger, LoggerFactory}

import scala.util.{Failure, Success, Try}

final case class ApplicationJson(data: Map[String, String])

object ApplicationJson {
  private implicit def jsonFormats: Formats = DefaultFormats

  private val logger: Logger = LoggerFactory.getLogger(getClass)

  private val weights = "weights"
  private val reasoncodeDesc = "reasoncode_descriptions"
  private val scoreComp = "scoreComponents"
  private val global = "GLOBAL"

  def empty: ApplicationJson = ApplicationJson(Map.empty)

  def parse(s: String, moduleName: String): ApplicationJson = {
    Try {
      val parsed = JsonMethods.parse(s)
      val aiReasonCodes = (parsed \ reasoncodeDesc \ moduleName).extract[Map[String, String]].keySet
      val result = (parsed \ weights \ scoreComp \ global).extract[Map[String, ReasonCode]]
      result.filter(m => m._2.reasoncode.nonEmpty && aiReasonCodes.contains(m._2.reasoncode.get))
    } match {
      case Success(value) => ApplicationJson(value.mapValues(rc => rc.reasoncode.get))
      case Failure(ex) =>
        logger.error("Exception while parsing reasoncodes desc per module response", ex)
        empty
    }
  }

}