package me.socure.ai.gateway.reasoncode.module

import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import me.socure.ai.gateway.reasoncode.{ReasonCodeResolver, ReasonCodeResolverImpl}
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.reasoncode.client.{ReasonCodeServiceClient, ReasonCodeServiceClientFactory}

import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.FiniteDuration

class AIGatewayReasonCodeModule extends AbstractModule {

  @Provides
  @Singleton
  def get(config: Config)(implicit ec: ExecutionContext): ReasonCodeResolver = {

    val reasoncodeServiceClient: ReasonCodeServiceClient = ReasonCodeServiceClientFactory.createV2(config)
    val cachePathPrefix = "reasoncode.service.cache"

    val cacheIntervalPath = s"$cachePathPrefix.refreshIntervalInMinutes"
    val cacheRetryInitialTimeoutMillis = s"$cachePathPrefix.retry.initialTimeoutMillis"
    val cacheMaxRetryAttempts = s"$cachePathPrefix.retry.maxAttempts"

    val cacheRefreshIntervalInMinutes = if (config.hasPath(cacheIntervalPath)) config.getInt(cacheIntervalPath) else 15
    val initialBackOffInMillis = if (config.hasPath(cacheRetryInitialTimeoutMillis)) config.getInt(cacheRetryInitialTimeoutMillis) else 100
    val maxAttempt = if (config.hasPath(cacheMaxRetryAttempts)) config.getInt(cacheMaxRetryAttempts) else 2
    val retryStrategy = RetryStrategy.constantBackoff(FiniteDuration(initialBackOffInMillis, TimeUnit.MILLISECONDS), maxAttempt)

    ReasonCodeResolverImpl.apply(reasoncodeServiceClient, cacheRefreshIntervalInMinutes, retryStrategy)
  }

}