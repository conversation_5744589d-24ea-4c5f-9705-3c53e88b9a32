package me.socure.ai.gateway.reasoncode

import com.github.tototoshi.csv.CSVReader
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.transaction.id.TrxId
import me.socure.reasoncode.client.ReasonCodeServiceClient
import org.json4s.{DefaultFormats, Formats}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, DoNotDiscover, FreeSpec, Ignore, Matchers}

import java.io.InputStreamReader
import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source

@DoNotDiscover
class ReasonCodeResolverImplTest extends FreeSpec with Matchers with ScalaFutures with MockitoSugar with BeforeAndAfter {

  private implicit def jsonFormats: Formats = DefaultFormats

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global
  implicit val trxID = TrxId("TEST-TRANSACTION")

  private val reasonCodeServiceClient = mock[ReasonCodeServiceClient]
  private lazy val appJsonData = Source.fromInputStream(getClass.getResourceAsStream("/applicationv3.json")).mkString

  before {
    Mockito.reset(reasonCodeServiceClient)
  }

  "test successful generation of reasoncodes" in {
    Mockito.when(reasonCodeServiceClient.getApplicationJson("v3.0")).thenReturn(
      Future(Right(appJsonData))
    )

    val reasonCodeResolver = new ReasonCodeResolverImpl(reasonCodeServiceClient, 10, RetryStrategy.times(1))

    whenReady(reasonCodeResolver.resolve(
      Map(
        "GLOBAL.300820" -> 1.0, //mapped to I301
        "GLOBAL.300825" -> 1.0, //mapped to R302
        "GLOBAL.300828" -> 0.0 //mapped to I307, but shouldn't fire as it is 0.0
      )
    )) { response =>
      response.isRight shouldBe true
      response match {
        case Right(reasoncodes) => reasoncodes shouldBe Set("I301", "R302")
        case Left(error) => fail(s"Test should pass in this case, got error ${error.code} ${error.message}")
      }
    }
  }

  "Run test data for reason code" in {

    val stream = getClass.getResourceAsStream("/rulecode_test_data.csv")
    val reader = new InputStreamReader(stream)
    val csvReader = CSVReader.open(reader)

    csvReader.toStreamWithHeaders
      .foreach(unfilteredRow => {
        val row = unfilteredRow.filter(!_._2.equals(""))

        val unfilteredGlobalRuleCodes: Map[String, String] = Map(

          "GLOBAL.300876" -> row.getOrElse("GLOBAL_ACC_STRUCTURE_ISSUE", ""),
          "GLOBAL.300820" -> row.getOrElse("GLOBAL_SAI_FNAME_MATCH_YES", ""),
          "GLOBAL.300821" -> row.getOrElse("GLOBAL_SAI_FNAME_MATCH_NO", ""),
          "GLOBAL.300822" -> row.getOrElse("GLOBAL_SAI_FNAME_MATCH_CONDITIONAL", ""),
          "GLOBAL.300824" -> row.getOrElse("GLOBAL_SAI_LNAME_MATCH_YES", ""),
          "GLOBAL.300825" -> row.getOrElse("GLOBAL_SAI_LNAME_MATCH_NO", ""),
          "GLOBAL.300826" -> row.getOrElse("GLOBAL_SAI_LNAME_MATCH_CONDITIONAL", ""),
          "GLOBAL.300840" -> row.getOrElse("GLOBAL_SAI_ADDRESS_MATCH_YES", ""),
          "GLOBAL.300841" -> row.getOrElse("GLOBAL_SAI_ADDRESS_MATCH_NO", ""),
          "GLOBAL.300842" -> row.getOrElse("GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL", ""),
          "GLOBAL.300844" -> row.getOrElse("GLOBAL_SAI_PHONE_MATCH_YES", ""),
          "GLOBAL.300845" -> row.getOrElse("GLOBAL_SAI_PHONE_MATCH_NO", ""),
          "GLOBAL.300846" -> row.getOrElse("GLOBAL_SAI_PHONE_MATCH_CONDITIONAL", ""),
          "GLOBAL.300870" -> row.getOrElse("GLOBAL_SAI_ROUTING_NUMBER_INVALID", ""),
          "GLOBAL.300872" -> row.getOrElse("GLOBAL_ACCOUNT_WARNING", ""),
          "GLOBAL.300873" -> row.getOrElse("GLOBAL_ACCOUNT_NO_INFO", ""),
          "GLOBAL.300877" -> row.getOrElse("GLOBAL_DECEASED_INDICATOR", ""),
          "GLOBAL.300878" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_15", ""),
          "GLOBAL.300879" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_30", ""),
          "GLOBAL.300880" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_45", ""),
          "GLOBAL.300881" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_60", ""),
          "GLOBAL.300882" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_75", ""),
          "GLOBAL.300883" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_90", ""),
          "GLOBAL.300884" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_120", ""),
          "GLOBAL.300885" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_150", ""),
          "GLOBAL.300886" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_180", ""),
          "GLOBAL.300887" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_210", ""),
          "GLOBAL.300888" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_240", ""),
          "GLOBAL.300889" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_270", ""),
          "GLOBAL.300891" -> row.getOrElse("GLOBAL_RETURN_HISTORY_INDICATOR", ""),
          "GLOBAL.300892" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_300", ""),
          "GLOBAL.300893" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_365", ""),
          "GLOBAL.300894" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_730", ""),
          "GLOBAL.300895" -> row.getOrElse("GLOBAL_LAST_SEEN_THRESHOLD_Y", "")
        )

        val globalRuleCodes = unfilteredGlobalRuleCodes.filter(!_._2.equals(""))
        val glDouble = globalRuleCodes.mapValues(_.toDouble)

        Mockito.when(reasonCodeServiceClient.getApplicationJson("v3.0")).thenReturn(
          Future(Right(appJsonData))
        )

        println(row.get("id"))

        val reasonCodeResolver = new ReasonCodeResolverImpl(reasonCodeServiceClient, 10, RetryStrategy.times(1))

        whenReady(reasonCodeResolver.resolve(
          glDouble
        )) { response =>
          response.isRight shouldBe true
          response match {
            case Right(reasoncodes) =>
              if(row.contains("I301")){
                assert(reasoncodes.contains("I301"))
              }
              if(row.contains("I302")){
                assert(reasoncodes.contains("I302"))
              }
              if(row.contains("I304")){
                assert(reasoncodes.contains("I304"))
              }
              if(row.contains("I305")){
                assert(reasoncodes.contains("I305"))
              }
              if(row.contains("I316")){
                assert(reasoncodes.contains("I316"))
              }
              if(row.contains("I317")){
                assert(reasoncodes.contains("I317"))
              }
              if(row.contains("I319")){
                assert(reasoncodes.contains("I319"))
              }
              if(row.contains("I320")){
                assert(reasoncodes.contains("I320"))
              }
              if(row.contains("R301")){
                assert(reasoncodes.contains("R301"))
              }
              if(row.contains("R302")){
                assert(reasoncodes.contains("R302"))
              }
              if(row.contains("R306")){
                assert(reasoncodes.contains("R306"))
              }
              if(row.contains("R307")){
                assert(reasoncodes.contains("R307"))
              }
              if(row.contains("R309")){
                assert(reasoncodes.contains("R309"))
              }
              if(row.contains("R311")){
                assert(reasoncodes.contains("R311"))
              }
              if(row.contains("R312")){
                assert(reasoncodes.contains("R312"))
              }
              if(row.contains("R313")){
                assert(reasoncodes.contains("R313"))
              }
              if (row.contains("R314")) {
                assert(reasoncodes.contains("R314"))
              }
              if (row.contains("I328")) {
                assert(reasoncodes.contains("I328"))
              }
              if (row.contains("I329")) {
                assert(reasoncodes.contains("I329"))
              }
              if (row.contains("I330")) {
                assert(reasoncodes.contains("I330"))
              }
              if (row.contains("I331")) {
                assert(reasoncodes.contains("I331"))
              }
              if (row.contains("I332")) {
                assert(reasoncodes.contains("I332"))
              }
              if (row.contains("I333")) {
                assert(reasoncodes.contains("I333"))
              }
              if (row.contains("I334")) {
                assert(reasoncodes.contains("I334"))
              }
              if (row.contains("I335")) {
                assert(reasoncodes.contains("I335"))
              }
              if (row.contains("I336")) {
                assert(reasoncodes.contains("I336"))
              }
              if (row.contains("I337")) {
                assert(reasoncodes.contains("I337"))
              }
              if (row.contains("I338")) {
                assert(reasoncodes.contains("I338"))
              }
              if (row.contains("I339")) {
                assert(reasoncodes.contains("I339"))
              }
              if (row.contains("I341")) {
                assert(reasoncodes.contains("I341"))
              }
              if (row.contains("I342")) {
                assert(reasoncodes.contains("I342"))
              }
              if (row.contains("I343")) {
                assert(reasoncodes.contains("I343"))
              }
              if (row.contains("I344")) {
                assert(reasoncodes.contains("I344"))
              }
              if (row.contains("R315")) {
                assert(reasoncodes.contains("R315"))
              }

            case Left(error) => fail(s"Test should pass in this case, got error ${error.code} ${error.message}")
          }
        }
      })

      }
}
