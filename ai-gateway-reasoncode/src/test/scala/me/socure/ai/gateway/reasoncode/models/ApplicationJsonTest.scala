package me.socure.ai.gateway.reasoncode.models

import org.scalatest.{FreeSpec, Matchers}

import scala.io.Source

class ApplicationJsonTest extends FreeSpec with Matchers {

  lazy val appV3JsonStr = Source.fromInputStream(getClass.getResourceAsStream("/applicationv3.json")).mkString

  "test valid appv3 json parsing" in {
    val parsedMap = ApplicationJson.parse(appV3JsonStr, "accountintelligence")
    parsedMap.data.nonEmpty shouldBe true
    parsedMap.data.size shouldBe 45
  }


  "test parsing invalid Json str" in {
    val invalidJsonStr =
      s"""{
         |   "name": "Some Name",
         |   "city": "Some City"
         |}""".stripMargin

    val parsedMap = ApplicationJson.parse(invalidJsonStr, "accountintelligence")
    parsedMap.data.isEmpty shouldBe true
  }

}
