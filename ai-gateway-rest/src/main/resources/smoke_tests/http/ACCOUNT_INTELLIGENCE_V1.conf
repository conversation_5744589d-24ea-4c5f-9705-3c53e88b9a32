name = "account_intelligence_v1"

request {
  method = "POST"
  endpoint = ${hostname}"/account/intelligence/v1"
  configurators = [
    {
      type = "socure_hmac"
      realm = "Socure"
      version = "1.0"
      strength = 512
      aws_secret_id = ${hmac.aws.secrets.manager.id}
    },
    {
      type = "disable_cert_validation"
    }
  ]
  headers = {
    "Content-Type" = "application/json"
  }
  body = {
    type = "classpath",
    value = "/smoke_tests/http/ACCOUNT_INTELLIGENCE_V1-"${env}".json"
  }
}

validations = [
  {
    validator = "=="
    json_path = "status_code"
    value = 200
  }
]