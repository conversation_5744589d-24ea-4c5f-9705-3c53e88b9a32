package me.socure.ai.gateway.rest

import io.swagger.v3.oas.models.OpenAPI
import me.socure.ai.gateway.rest.servlet.AIGatewayServlet
import me.socure.ai.trice.servlet.TriceAPIServlet
import me.socure.common.check.impl.http._
import me.socure.common.check.servlet.api.CheckerServlet
import me.socure.common.healthcheck.checker.HealthCheckProcessorBasedHealthChecker
import me.socure.common.healthcheck.servlet.HealthCheckServlet
import me.socure.common.jetty.AbstractJettyService
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.openapi3.scalatra.OpenApiScalatraServlet
import me.socure.common.servlet.metrics.ServletMetricsFilter
import me.socure.support.timeout.ApiTimeout
import org.eclipse.jetty.servlet.{FilterHolder, ServletHolder}
import org.eclipse.jetty.util.thread.ThreadPool
import org.scalatra.CorsSupport
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import java.util.{EnumSet => JEnumSet}
import javax.servlet.DispatcherType
import scala.concurrent.ExecutionContext


class AIGatewayRestService(val threadPool: ThreadPool,
                           val port: Int,
                           val apiTimeout: ApiTimeout,
                           val corsAllowedDomains: Set[String],
                           val healthChecker: HealthCheckProcessorBasedHealthChecker,
                           val openApi: OpenAPI,
                           val aiGatewayServlet: AIGatewayServlet,
                           val triceAPIServlet: TriceAPIServlet
                          )(implicit executionContext: ExecutionContext)
  extends AbstractJettyService(
    threadPool = threadPool,
    port = port,
    apiTimeout = Some(apiTimeout.value)
  ) {

  import AIGatewayRestService._

  override def startUp(): Unit = {

    val servletMetricsFilter = new ServletMetricsFilter(
      metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix),
      baseTags = MetricTags(serviceName = Some("ai-gateway-service"))
    )
    val dispatchTypes = JEnumSet.of(
      DispatcherType.REQUEST,
      DispatcherType.FORWARD,
      DispatcherType.ASYNC,
      DispatcherType.ERROR,
      DispatcherType.INCLUDE
    )
    getHandler.addFilter(new FilterHolder(servletMetricsFilter), "/*", dispatchTypes)

    val healthCheckServletHolder = new ServletHolder(new HealthCheckServlet(healthChecker))
    getHandler.addServlet(healthCheckServletHolder, "/healthcheck/*")

    val openApiScalatraServlet = new OpenApiScalatraServlet(openApi) with CorsSupport {
      after() {
        response.setHeader("Access-Control-Allow-Origin", corsAllowedDomains.mkString(","))
        response.setHeader("Strict-Transport-Security", "max-age=86400; includeSubDomains")
        response.setHeader("Cache-Control", "no-cache")
      }

      options("/*") {
        response.setHeader("Access-Control-Allow-Headers", request.getHeader("Access-Control-Request-Headers"))
      }
    }

    getHandler.addServlet(new ServletHolder(openApiScalatraServlet), "/spec/*")
    getHandler.addServlet(new ServletHolder(aiGatewayServlet), "/account/intelligence/v1/*")
      getHandler.addServlet(new ServletHolder(triceAPIServlet), "/trice/*")

    val smokeTestServlet = CheckerServlet(
      HttpCheckerAutoLoader
    )
    getHandler.addServlet(new ServletHolder(smokeTestServlet), "/smoke-tests/*")

    getServer.start()
    logger.info("AI Gateway service started successfully !!!")
  }

  override def shutDown(): Unit = {
    logger.info("Terminating AI Gateway service...")
    getServer.stop()
    logger.info("AI Gateway service stopped successfully !!!")
  }

}

object AIGatewayRestService {
  private val logger: Logger = LoggerFactory.getLogger(getClass)
}
