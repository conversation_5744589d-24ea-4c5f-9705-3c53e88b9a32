package me.socure.ai.gateway.rest.module

import com.google.common.util.concurrent.AbstractIdleService
import com.google.inject.AbstractModule
import com.google.inject.multibindings.Multibinder
import me.socure.ai.gateway.rest.AIGatewayRestService
import me.socure.ai.gateway.rest.provider.AIGatewayRestServiceProvider
import me.socure.common.jmx.SocureMicroJMXService

class AIGatewayRestServiceModule extends AbstractModule {

  override def configure(): Unit = {
    val b = Multibinder.newSetBinder(binder(), classOf[AbstractIdleService])
    b.addBinding().to(classOf[AIGatewayRestService])
    b.addBinding().to(classOf[SocureMicroJMXService])
    bind(classOf[AIGatewayRestService]).toProvider(classOf[AIGatewayRestServiceProvider]).asEagerSingleton()
  }

}
