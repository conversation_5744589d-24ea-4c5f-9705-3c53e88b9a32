package me.socure.ai.gateway.rest.provider

import com.google.inject.{Inject, Provider}
import com.typesafe.config.Config
import io.swagger.v3.oas.models.OpenAPI
import me.socure.ai.gateway.rest.AIGatewayRestService
import me.socure.ai.gateway.rest.servlet.AIGatewayServlet
import me.socure.ai.trice.servlet.TriceAPIServlet
import me.socure.common.healthcheck.checker.HealthCheckProcessorBasedHealthChecker
import me.socure.common.healthcheck.jvm.JvmHealthCheckProcessorFactory
import me.socure.common.jettythreadpool.factory.JettyThreadPoolFactory
import me.socure.support.timeout.ApiTimeout

import java.util.concurrent.{ThreadPoolExecutor, TimeUnit}
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.{Duration, FiniteDuration}

class AIGatewayRestServiceProvider @Inject()(config: Config,
                                             openApi: OpenAPI,
                                             executor: ThreadPoolExecutor,
                                             aiGatewayServlet: AIGatewayServlet,
                                             triceAPIServlet: TriceAPIServlet
                                            )(implicit executionContext: ExecutionContext)
  extends Provider[AIGatewayRestService] {

  override def get(): AIGatewayRestService = {
    val port = config.getInt("server.port")
    val corsAllowedDomains = config.getStringList("cors.allowedDomains").asScala.toSet
    val healthCheckSettings = config.getConfig("healthcheck.thresholds")
      .entrySet()
      .asScala.map(r => r.getKey -> r.getValue.unwrapped().toString)
      .toMap

    val threadPool = JettyThreadPoolFactory.get(executor)

    val healthCheckProcessor = JvmHealthCheckProcessorFactory.create(thresholds = healthCheckSettings)

    val healthChecker = new HealthCheckProcessorBasedHealthChecker(
      healthCheckProcessor = healthCheckProcessor
    )
    val timeoutConfig = config.getString("server.apiTimeout")
    val apiTimeout: ApiTimeout = ApiTimeout(Duration(timeoutConfig) match {
      case d if d.isFinite() => FiniteDuration(d.toMillis, TimeUnit.MILLISECONDS)
      case d => throw new IllegalStateException(s"Expected a finite duration for server.apiTimeout but found $d")
    })

    new AIGatewayRestService(
      threadPool = threadPool,
      port = port,
      apiTimeout = apiTimeout,
      corsAllowedDomains = corsAllowedDomains,
      healthChecker = healthChecker,
      openApi = openApi,
      aiGatewayServlet = aiGatewayServlet,
      triceAPIServlet = triceAPIServlet
    )
  }

}