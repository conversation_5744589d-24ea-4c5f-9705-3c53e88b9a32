package me.socure.ai.gateway.rest.provider

import com.google.inject.{Inject, Provider}
import com.typesafe.config.Config
import me.socure.common.clock.RealClock
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerifierFactory}

import scala.concurrent.ExecutionContext

class HMACHttpVerifierProvider @Inject()(config: Config)
                                        (implicit executionContext: ExecutionContext) extends Provider[HMACHttpVerifier] {

  override def get(): HMACHttpVerifier = {
    val hmacConfig = config.getConfig("hmac")
    HmacVerifierFactory.createSingle(hmacConfig, new RealClock)
  }

}