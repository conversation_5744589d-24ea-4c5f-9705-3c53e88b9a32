package me.socure.ai.gateway.rest.servlet

import com.google.inject.Inject
import io.swagger.v3.oas.models.OpenAPI
import me.socure.ai.gateway.common.models.AIGatewayRequest
import me.socure.ai.gateway.vendor.service.AIGatewayVendorService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.{ErrorResponse, ResponseStatus}
import org.json4s.ext.EnumNameSerializer
import org.json4s.{DefaultFormats, Formats}
import org.scalatra.json.JacksonJsonSupport
import org.scalatra.{FutureSupport, ScalatraServlet}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AIGatewayServlet @Inject()(val hmacVerifier: HMACHttpVerifier,
                                 val openApi: OpenAPI,
                                 val aiGatewayVendorService: AIGatewayVendorService
                                )(implicit val executor: ExecutionContext) extends
  ScalatraServlet with JacksonJsonSupport with FutureSupport with AuthenticationSupport with OpenApiScalatraSupport {

  override protected implicit def jsonFormats: Formats = DefaultFormats ++ Seq(new EnumNameSerializer(ResponseStatus))

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)

  before() {
    contentType = formats("json")
    //validateRequest()
  }

  post("/") {
    try {
      val req = parsedBody.extract[AIGatewayRequest]
      ScalatraResponseFactory.get {
        metrics.timeFuture("request.validate") {
          aiGatewayVendorService.process(req)
        }
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.request")
        logger.error("Failed to parse user input", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

}