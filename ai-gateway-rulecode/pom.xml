<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-gateway</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ai-gateway-rulecode</artifactId>
    <version>${revision}</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <socure.commons.version>${revision}</socure.commons.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-transaction-aware-logger</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-logs</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-rulecode</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-gateway-common</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice-client</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_2.11</artifactId>
            <version>3.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.nrinaudo</groupId>
            <artifactId>kantan.csv_2.11</artifactId>
            <version>0.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.tototoshi</groupId>
            <artifactId>scala-csv_${sc.ver}</artifactId>
            <version>1.3.4</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
