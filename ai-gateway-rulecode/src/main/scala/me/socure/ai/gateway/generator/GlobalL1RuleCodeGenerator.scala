package me.socure.ai.gateway.generator

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.util.Try
import me.socure.ai.gateway.common.models.{AIGatewayGlobalRulecodes, AIGatewayRuleCodes, RulecodeRequest}

object GlobalL1RuleCodeGenerator {
  val Vendor = "GLOBAL_L1"
  private val TriceRuleCode100005 = "TRICE_100005"
  private val globalRuleCode = "GLOBAL.300896"
  private val AcceptedString = "accepted"
  private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

  def generateThresholdRuleCode(
                                 rulecodeRequest: RulecodeRequest,
                                 codeRuleCodes: AIGatewayRuleCodes,
                                 l1RuleCodes: AIGatewayGlobalRulecodes,
                                 transactionId: String
                               ): Option[(String, Double)] = {
    val maxLastSeenDate = codeRuleCodes.categoricalRulecodes.get(globalRuleCode).flatMap(parseDate)

    val thresholdGlobals: Seq[(String, (RulecodeRequest, String) => Option[Double])] = Seq(
      "GLOBAL.300878" -> thresholdCheck(maxLastSeenDate, -1,15),
      "GLOBAL.300879" -> thresholdCheck(maxLastSeenDate, 15, 30),
      "GLOBAL.300880" -> thresholdCheck(maxLastSeenDate, 30, 45),
      "GLOBAL.300881" -> thresholdCheck(maxLastSeenDate, 45, 60),
      "GLOBAL.300882" -> thresholdCheck(maxLastSeenDate, 60, 75),
      "GLOBAL.300883" -> thresholdCheck(maxLastSeenDate, 75, 90),
      "GLOBAL.300884" -> thresholdCheck(maxLastSeenDate, 90, 120),
      "GLOBAL.300885" -> thresholdCheck(maxLastSeenDate, 120, 150),
      "GLOBAL.300886" -> thresholdCheck(maxLastSeenDate, 150, 180),
      "GLOBAL.300887" -> thresholdCheck(maxLastSeenDate, 180, 210),
      "GLOBAL.300888" -> thresholdCheck(maxLastSeenDate, 210, 240),
      "GLOBAL.300889" -> thresholdCheck(maxLastSeenDate, 240, 270),
      "GLOBAL.300892" -> thresholdCheck(maxLastSeenDate, 270, 300),
      "GLOBAL.300893" -> thresholdCheck(maxLastSeenDate, 300, 365),
      "GLOBAL.300894" -> thresholdCheck(maxLastSeenDate, 365, 730),
      "GLOBAL.300895" -> thresholdCheck(maxLastSeenDate, 730, Int.MaxValue)
    )

    //Iterate over the logic list using the conditional threshold
    thresholdGlobals.foldLeft(Option.empty)((result, element) => {
      val value = element._2.apply(rulecodeRequest, transactionId)
      if (value.isDefined) {
        return Some((element._1, value.get))
      }
      result
    })
  }

  def globalVRVALSSNDeceasedCheck(ruleCodeRequest: RulecodeRequest): Option[Double] = {
    val aiGatewayRuleCodes = ruleCodeRequest.ruleCodes
    val VR_SSN_DECEASED = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100003", 0.0)
    VR_SSN_DECEASED match {
      case 1.0 => Some(1.0)
      case _ => None
    }
  }

  def globalVRVALIdentityIsDeceased(ruleCodeRequest: RulecodeRequest): Option[Double] = {
    val aiGatewayRuleCodes = ruleCodeRequest.ruleCodes
    val vrSsnNotInPublicRec = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100062", 0.0)
    val vrEmergingIdentity = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100115", 0.0)
    val vrIdentityIsDeceased = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100009", 0.0)
    val vrSsnDeceased = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100003", 0.0)
    val vrFirstnameScore = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100201", 0.0)
    val vrSsnScore = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100203", 0.0)
    val vrDobScore = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100204", 0.0)
    val vrSsnMiskeyed = aiGatewayRuleCodes.numericalRulecodes.getOrElse("VRVAL_100041", 0.0)

    (vrSsnNotInPublicRec, vrEmergingIdentity, vrIdentityIsDeceased, vrSsnDeceased,
      vrFirstnameScore, vrSsnScore, vrDobScore, vrSsnMiskeyed) match {
      case (1.0, _, _, _, _, _, _, _) => None
      case (_, 1.0, _, _, _, _, _, _) => None
      case (_, _, 1.0, _, _, _, _, _) => Some(1.0)
      case (_, _, _, 1.0, 0.99, 0.99, 0.99, miskeyed) =>
        if (miskeyed == 1.0) None else Some(1.0)
      case _ => None
    }
  }


  private def thresholdCheck(lastSeenDateOpt: Option[LocalDate], minDays: Int, maxDays: Int = Int.MaxValue): (RulecodeRequest, String) => Option[Double] = {
    (rulecodeRequest: RulecodeRequest, transactionId: String) =>
      lastSeenDateOpt match {
        case Some(lastSeenDate) =>
          val today = LocalDate.now()
          val daysSinceLastSeen = java.time.temporal.ChronoUnit.DAYS.between(lastSeenDate, today)
          if (daysSinceLastSeen > minDays && daysSinceLastSeen <= maxDays) toNumericalRuleCode(Some(true))
          else None
        case None => None
      }
  }

  private def parseDate(dateStr: String): Option[LocalDate] = {
    Try(LocalDate.parse(dateStr, formatter)).toOption
  }

  private def toNumericalRuleCode(result: Option[Boolean]) = {
    result.flatMap(if (_) Some(1.0) else None)
  }

  private def equalityCheckOptionalString(stringOption: Option[String], comparisonString: String) = {
    stringOption.map(_.equalsIgnoreCase(comparisonString))
  }
}
