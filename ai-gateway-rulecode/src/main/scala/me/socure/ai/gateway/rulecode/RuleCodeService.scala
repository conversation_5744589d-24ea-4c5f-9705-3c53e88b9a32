package me.socure.ai.gateway.rulecode

import com.google.inject.Inject
import me.socure.ai.gateway.common.models.AIGatewayInquiries.{AVAILABILITY, OWNERSHIP}
import me.socure.ai.gateway.common.models.vendor.AIVendorResponse
import me.socure.ai.gateway.common.models._
import me.socure.ai.gateway.common.util._
import me.socure.ai.gateway.generator.GlobalL1RuleCodeGenerator
import me.socure.ai.gateway.rulecode.RuleCodeService._
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.models.MetricTags.transactionId
import me.socure.common.models.RuleCodeConfigurationV2
import me.socure.common.service.RuleCodeGenerationService
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import org.json4s.{DefaultFormats, Extraction, Formats}

import java.util.concurrent.atomic.AtomicReference
import scala.concurrent.{ExecutionContext, Future}


class RuleCodeService @Inject()(ruleCodeGeneratorV2: RuleCodeGenerationService[RuleCodeConfigurationV2],
                                ruleCodeConfigurationV2: AtomicReference[RuleCodeConfigurationV2],
                                scorer: Scorer
                               ) {

  private implicit val formats: Formats = DefaultFormats
  private val txnLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)

  def computeVendorRulecodes(transactionId: String, accountId: Long, vendorResponses: Seq[AIVendorResponse], request: AIGatewayRequest)(implicit ec: ExecutionContext): Future[Option[AIGatewayGlobalRulecodes]] = {
    implicit val trxId = TrxId(transactionId)
    val codeRuleCodes = generateCodeRulcodes(vendorResponses, request, transactionId)
    var nRuleCodeMap = codeRuleCodes.numericalRulecodes
    var cRuleCodeMap = codeRuleCodes.categoricalRulecodes
    val ruleCodes = transformVendorRuleCodes(vendorResponses)
    val imputedVendorRuleCodes = imputeVendorRuleCodes(ruleCodes)
    val rulecodeRequest = RulecodeRequest(imputedVendorRuleCodes, transactionId, accountId, "GLOBAL_L1")
    // Global RuleCode Generation Starts Here
    val rulecodeResponse = processRuleCodes(rulecodeRequest, transactionId)
    rulecodeResponse.flatMap {
      case Some(l1RuleCodes) =>
        val thresholdRuleCode = GlobalL1RuleCodeGenerator.generateThresholdRuleCode(rulecodeRequest, codeRuleCodes, l1RuleCodes, transactionId)
        thresholdRuleCode.foreach(a => nRuleCodeMap += a)
        GlobalL1RuleCodeGenerator.globalVRVALSSNDeceasedCheck(rulecodeRequest).foreach { value =>
          nRuleCodeMap += ("GLOBAL.300897" -> value)
        }
        GlobalL1RuleCodeGenerator.globalVRVALIdentityIsDeceased(rulecodeRequest).foreach { value =>
          nRuleCodeMap += ("GLOBAL.300898" -> value)
        }
        l1RuleCodes.numericalRulecodes.foreach(a => nRuleCodeMap += a)
        l1RuleCodes.categoricalRulecodes.foreach(a => cRuleCodeMap += a)
        val ruleCodesFroml1 = Some(AIGatewayGlobalRulecodes.apply(
          "GLOBAL",
          nRuleCodeMap,
          cRuleCodeMap
        ))
        val transformedl1RuleCodes = transformGlobalRuleCodes(ruleCodesFroml1)
        val transformedl1CRuleCodes = transformedl1RuleCodes.categoricalRulecodes ++ imputedVendorRuleCodes.categoricalRulecodes
        val transformedl1NRuleCodes = transformedl1RuleCodes.numericalRulecodes ++ imputedVendorRuleCodes.numericalRulecodes

        val imputedl1RuleCodes = AIGatewayRuleCodes(transformedl1NRuleCodes, transformedl1CRuleCodes)
        val l2RuleCodeRequest = RulecodeRequest(imputedl1RuleCodes, transactionId, accountId, "GLOBAL_L2")
        val l2RulecodeResponse = processRuleCodes(l2RuleCodeRequest, transactionId)
        l2RulecodeResponse.flatMap {
          case Some(l2RuleCodes) =>
            l2RuleCodes.numericalRulecodes.foreach(a => nRuleCodeMap += a)
            l2RuleCodes.categoricalRulecodes.foreach(a => cRuleCodeMap += a)
            val ruleCodesFroml2 = Some(AIGatewayGlobalRulecodes.apply(
              "GLOBAL",
              nRuleCodeMap,
              cRuleCodeMap
            ))
            val transformedl2RuleCodes = transformGlobalRuleCodes(ruleCodesFroml2)
            val l3RuleCodeRequest = RulecodeRequest(transformedl2RuleCodes, transactionId, accountId, "GLOBAL_L3")
            val l3RulecodeResponse = processRuleCodes(l3RuleCodeRequest, transactionId)
            l3RulecodeResponse.flatMap {
              case Some(l3RuleCodes) =>
                l3RuleCodes.numericalRulecodes.foreach(a => nRuleCodeMap += a)
                l3RuleCodes.categoricalRulecodes.foreach(a => cRuleCodeMap += a)
                Future.successful(Some(AIGatewayGlobalRulecodes.apply(
                  "GLOBAL",
                  nRuleCodeMap,
                  cRuleCodeMap
                )))
            }
        }
    }
  }

  private def generateBNYMGlobalScore(
                                       request: AIGatewayRequest,
                                       rulecodeRequest: RulecodeRequest
                                     ) = {
    val BNYVLRuleCode100001 = "BNYVL_100001"
    val BNYVLRuleCode100020 = "BNYVL_100020"

    val Valid = "VALID"
    val Unknown = "UNKNOWN"
    val Invalid = "INVALID"
    val Y = "Y"
    val N = "N"
    val GlobalRuleCode300817 = "GLOBAL.300817"
    val DefaultRuleCodeValue = Some(GlobalRuleCode300817 -> 0.5)

    val bnymAsvStatusCode = rulecodeRequest.ruleCodes.categoricalRulecodes.get(BNYVLRuleCode100001).map(_.trim)
    val bnymEwsDirectContributor = rulecodeRequest.ruleCodes.categoricalRulecodes.get(BNYVLRuleCode100020).map(_.trim)

    val containsBnyvl = request.vendors.exists(_.name == BNYVL_NAME)

    if (containsBnyvl) {
      (bnymAsvStatusCode, bnymEwsDirectContributor) match {
        case (Some(statusCode), Some(directContributor)) =>
          if (statusCode.equalsIgnoreCase(Valid) && directContributor.equalsIgnoreCase(Y)) {
            Some(GlobalRuleCode300817 -> 0.95)
          } else if (statusCode.equalsIgnoreCase(Valid) && directContributor.equalsIgnoreCase(N)) {
            Some(GlobalRuleCode300817 -> 0.93)
          } else if (statusCode.equalsIgnoreCase(Unknown) && directContributor.equalsIgnoreCase(Y)) {
            Some(GlobalRuleCode300817 -> 0.4)
          } else if (statusCode.equalsIgnoreCase(Unknown) && directContributor.equalsIgnoreCase(N)) {
            Some(GlobalRuleCode300817 -> 0.68)
          } else if (statusCode.equalsIgnoreCase(Invalid) && directContributor.equalsIgnoreCase(Y)) {
            Some(GlobalRuleCode300817 -> 0.09)
          } else if (statusCode.equalsIgnoreCase(Invalid) && directContributor.equalsIgnoreCase(N)) {
            Some(GlobalRuleCode300817 -> 0.1)
          } else {
            None
          }
        // Adding these cases as default 0.5 as confirmed with Michal. We can update this later
        case (None, Some(directContributor)) => DefaultRuleCodeValue
        case (Some(_), None) => DefaultRuleCodeValue
        case (None, None) => DefaultRuleCodeValue
      }
    } else {
      DefaultRuleCodeValue
    }
  }

  def transformVendorRuleCodes(vendorResponses: Seq[AIVendorResponse]): AIGatewayRuleCodes = {

    val (numeric, categorical) = vendorResponses.foldLeft((Map.empty[String, Double], Map.empty[String, String])) { case (overall, current) =>
      current.rulecodes match {
        case Some(rc) =>
          val updatedCRuleCodes = rc.categoricalRulecodes.map { case (key, value) => key.replace('.', '_') -> refMap.getOrElse(value, value) }
          val updatedNRuleCodes = rc.numericalRulecodes.map { case (key, value) => key.replace('.', '_') -> value }
          (overall._1 ++ updatedNRuleCodes, overall._2 ++ updatedCRuleCodes)
        case None => overall
      }
    }
    AIGatewayRuleCodes(numeric, categorical)
  }

  def imputeVendorRuleCodes(ruleCodes: AIGatewayRuleCodes): AIGatewayRuleCodes = {

    var cRuleCodes = ruleCodes.categoricalRulecodes

    if (!ruleCodes.categoricalRulecodes.contains(BNYVL_SSN)) {
      cRuleCodes += (BNYVL_SSN -> UNKNOWN)
    }
    if (!ruleCodes.categoricalRulecodes.contains(BNYVL_DOB)) {
      cRuleCodes += (BNYVL_DOB -> UNKNOWN)
    }

    AIGatewayRuleCodes(ruleCodes.numericalRulecodes, cRuleCodes)
  }

  def transformGlobalRuleCodes(globalRuleCodes: Some[AIGatewayGlobalRulecodes]): AIGatewayRuleCodes = {

    val (numeric, categorical) = globalRuleCodes.foldLeft((Map.empty[String, Double], Map.empty[String, String])) { case (overall, current) =>
      val updatedNRuleCodes = current.numericalRulecodes.map { case (k, v) => k.replace('.', '_') -> v }
      val updatedCRuleCodes = current.categoricalRulecodes.map { case (k, v) => k.replace('.', '_') -> v }
      (overall._1 ++ updatedNRuleCodes, overall._2 ++ updatedCRuleCodes)
    }

    AIGatewayRuleCodes.apply(numeric, categorical)
  }

  def processRuleCodes(rulecodeRequest: RulecodeRequest, transactionId: String)(implicit ec: ExecutionContext): Future[Option[AIGatewayGlobalRulecodes]] = {
    implicit val trxId = TrxId(transactionId)
    val ruleCodeResponse = ruleCodeGeneratorV2.generate(Extraction.decompose(rulecodeRequest), ruleCodeConfigurationV2.get())
    ruleCodeResponse.flatMap {
      ruleCodeV2Response =>
        ruleCodeV2Response.response match {
          case Right(successResponse) =>
            Future.successful(
              Some(AIGatewayGlobalRulecodes.apply(
                "GLOBAL",
                successResponse.numericalRuleCodes.map(a => a.name -> a.value).toMap,
                successResponse.categoricalRuleCodes.map(a => a.name -> a.value).toMap
              ))
            )
          case Left(errorResponse: ErrorResponse) =>
            txnLogger.error(s"Rulecode computation failure errorResponse : ${errorResponse.message} , ${errorResponse.code} ")
            Future.successful(None)
        }
    } recoverWith {
      case e: Throwable =>
        txnLogger.error(s"computeVendorRulecodes failure errorResponse : ", e)
        Future.successful(None)
    }
  }

  def generateCodeRulcodes(responses: Seq[AIVendorResponse], request: AIGatewayRequest, transactionId: String): AIGatewayRuleCodes = {
    implicit val trxId = TrxId(transactionId)
    val cRuleCodes: scala.collection.mutable.Map[String, String] = scala.collection.mutable.Map[String, String]()
    val nRuleCodes: scala.collection.mutable.Map[String, Double] = scala.collection.mutable.Map[String, Double]()
    responses.foreach(res => {
      if (res.rulecodes.get.categoricalRulecodes.nonEmpty) { // We accumulate all the rule codes, which will be limited in number
        cRuleCodes ++= res.rulecodes.get.categoricalRulecodes.filterKeys(ScoreHelper.logicKeys ++ ScoreHelper.availabilityLogicKeys)
      }

      if (res.rulecodes.get.numericalRulecodes.nonEmpty) {
        nRuleCodes ++= res.rulecodes.get.numericalRulecodes.filterKeys(ScoreHelper.numericLogicKeys)
      }
    })

    val containsBnyvl = request.vendors.exists(_.name == BNYVL_NAME)

    if (containsBnyvl) {
      val availabilityResult = ScoreHelper.processAvailabilityRuleCodes(request, cRuleCodes)
      val ownershipResult = if(updateInquiries(request.payment.inquiries).contains(AIGatewayInquiries.OWNERSHIP)) {
        Some(ScoreHelper.processRuleCodes(request, cRuleCodes, nRuleCodes))
      } else None

      val scores = getScores(request, availabilityResult, ownershipResult)
      updateRuleCodesAfterScoreComputation(scores, availabilityResult.asvPattern, availabilityResult.saiAccountStructureIssue, ownershipResult, availabilityResult.maxLastSeenDate)
    } else {
      val availabilityResult: MbOnlyAvailabilityResult = ScoreHelper.processMbOnlyAvailabilityRuleCodes(request, cRuleCodes)
      val ownershipResult = if(updateInquiries(request.payment.inquiries).contains(AIGatewayInquiries.OWNERSHIP)) {
        Some(ScoreHelper.processRuleCodes(request, cRuleCodes, nRuleCodes))
      } else None

      val scores = getMbOnlyScores(request, availabilityResult, ownershipResult)
      // saiAccountStructureIssue is always set to false as it is triggered when BNYM and MBT responses were contradicting
      updateRuleCodesAfterScoreComputation(scores, availabilityResult.asvPattern, availabilityResult.saiAccountStructureIssue, ownershipResult, availabilityResult.maxLastSeenDate)
    }
  }

  private def updateRuleCodesAfterScoreComputation(scores: AIGatewayScore, asvResult:String, saiAccountStructureIssue: Boolean, oResult: Option[OwnershipResult], maxLastSeenDate: String): AIGatewayRuleCodes = {
    implicit val trxId = TrxId(transactionId)
    var nRuleCodeMap = Map.empty[String, Double]

    scores match {
      case AIGatewayScore(Some(-1), _, _, _) =>
        txnLogger.error(s"Status score lookup failed ${scores.availabilityScore}, ${scores.ownershipScore}")
        throw ScoreException("Status score lookup failed")
      case AIGatewayScore(_, Some(-1), _, _) =>
        txnLogger.error(s"Ownership score lookup failed ${scores.availabilityScore}, ${scores.ownershipScore}")
        throw ScoreException("Ownership score lookup failed")
      case _ =>
        scores.availabilityScore match {
          case Some(availabilityScore) => nRuleCodeMap += ("GLOBAL.300809" -> availabilityScore)
          case _ => // this handles cases when only ownership inquiry is being called
        }
        scores.ownershipScore match {
          case Some(ownershipScore) => nRuleCodeMap += ("GLOBAL.300810" -> ownershipScore)
          case _ => // this handles cases when only availability inquiry is being called
        }
        scores.availabilityScoreBreakDown match {
          case Some(scoreBreakdown) =>
            scoreBreakdown.mbltScore.map(score => nRuleCodeMap += ("GLOBAL.300815" -> score))
            scoreBreakdown.triceScore.map(score => nRuleCodeMap += ("GLOBAL.300816" -> score))
            scoreBreakdown.bnymScore.map(score => nRuleCodeMap += ("GLOBAL.300817" -> score))
            scoreBreakdown.convlScore.map(score => nRuleCodeMap += ("GLOBAL.300818" -> score))
          case _ => //no rule codes to add
        }
    }

    val asvPatternBasedOnScores: String = scores match {
      case AIGatewayScore(_, _, Some(availabilityScoreBreakdown), _) => {
        availabilityScoreBreakdown match {
          case AIGatewayScoreBreakdown(Some(mbltScore), Some(bnymScore), Some(triceScore), Some(convlScore)) =>
            List(extractDecimalPartAsNumber(triceScore), extractDecimalPartAsNumber(bnymScore), extractDecimalPartAsNumber(convlScore), extractDecimalPartAsNumber(mbltScore)).mkString("_")
          case _ => ""
        }
      }
      case _ => ""
    }

    val global300811 =
      if (asvPatternBasedOnScores.isEmpty) asvResult
      else asvPatternBasedOnScores

    // Imputing address, ssn and dob rulecodes to Unknown in case the PII elements is missing
    // Certain rulecodes only to be generated on the basis of ownership result
    val cRuleCodeMap = oResult map { result =>
      Map(
        "GLOBAL.300801" -> result.firstNameMatch,
        "GLOBAL.300802" -> result.lastNameMatch,
        "GLOBAL.300803" -> result.busNameMatch,
        "GLOBAL.300804" -> result.phoneMatch,
        "GLOBAL.300806" -> result.ssnMatch,
        "GLOBAL.300807" -> result.dobMatch,
        "GLOBAL.300811" -> global300811,
        "GLOBAL.300812" -> result.aovPattern,
        "GLOBAL.300813" -> result.socIdSsnMatch,
        "GLOBAL.300814" -> result.socIdDobMatch,
        "GLOBAL.300805" -> result.addressMatch,
        "GLOBAL.300896" -> result.maxLastSeenDate
      )
    } getOrElse {
      Map(
        "GLOBAL.300811" -> global300811,  //ASV Pattern
        "GLOBAL.300896" -> maxLastSeenDate
      )
    }

    //CON-419: check for account structure issue
    saiAccountStructureIssue match {
      case true => nRuleCodeMap += ("GLOBAL.300876" -> 1.0)
      case _ => nRuleCodeMap
    }

    AIGatewayRuleCodes(nRuleCodeMap, cRuleCodeMap)
  }

  def getScores(request: AIGatewayRequest, availabilityResult: AvailabilityResult, matchResult: Option[OwnershipResult]): AIGatewayScore = {
    implicit val trxId = TrxId(request.metadata.transactionId)

    updateInquiries(request.payment.inquiries) match {
      case set if set == Set(AVAILABILITY) =>
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val bnymStatusScore = scorer.getBnymAvailabilityScore(availabilityResult.bnymStatusCode, availabilityResult.ewsDirectContributor)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)

        val statusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, bnymStatusScore, convlStatusScore)

        AIGatewayScore(
          Some(statusScore),
          None,
          Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(bnymStatusScore), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          None
        )
      case set if set == Set(OWNERSHIP) =>
        val ownershipScore = scorer.getOwnershipScore(matchResult.get, request.params.businessName)
        AIGatewayScore(None, Some(ownershipScore), None, None)
      case set if set.contains(AVAILABILITY) && set.contains(OWNERSHIP) =>
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val bnymStatusScore = scorer.getBnymAvailabilityScore(availabilityResult.bnymStatusCode, availabilityResult.ewsDirectContributor)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)
        val statusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, bnymStatusScore, convlStatusScore)
        val ownershipScore = scorer.getOwnershipScore(matchResult.get, request.params.businessName)
        AIGatewayScore(
          Some(statusScore),
          Some(ownershipScore),
          Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(bnymStatusScore), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          None
        )
      case _ =>  //This case can only be triggered via direct call to ai-gateway-service but not via ID+
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val bnymStatusScore = scorer.getBnymAvailabilityScore(availabilityResult.bnymStatusCode, availabilityResult.ewsDirectContributor)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)
//        val statusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, bnymStatusScore, convlStatusScore)
//        val ownershipScore = scorer.getOwnershipScore(matchResult.get, request.params.businessName)
        AIGatewayScore(
          None,  //Some(statusScore),
          None,  //Some(ownershipScore),
          Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(bnymStatusScore), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          None
        )
    }
  }

  def getMbOnlyScores(request: AIGatewayRequest, availabilityResult: MbOnlyAvailabilityResult, ownershipResult: Option[OwnershipResult]): AIGatewayScore = {
    implicit val trxId = TrxId(request.metadata.transactionId)

    updateInquiries(request.payment.inquiries) match {
      case set if set == Set(AVAILABILITY) =>
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)

        val globalStatusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, convlStatusScore)
        AIGatewayScore(
          availabilityScore = Some(globalStatusScore),
          ownershipScore = None,
          availabilityScoreBreakDown = Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(0.5), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          ownershipScoreBreakDown = None
        )
      case set if set == Set(OWNERSHIP) =>
        val ownershipScore = scorer.getOwnershipScore(ownershipResult.get, request.params.businessName)
        AIGatewayScore(
          availabilityScore = None,
          ownershipScore = Some(ownershipScore),
          availabilityScoreBreakDown = None,
          ownershipScoreBreakDown = None
        )
      case set if set.contains(AVAILABILITY) && set.contains(OWNERSHIP) =>
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)
        val globalStatusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, convlStatusScore)
        val ownershipScore = scorer.getOwnershipScore(ownershipResult.get, request.params.businessName)
        AIGatewayScore(
          availabilityScore = Some(globalStatusScore),
          ownershipScore = Some(ownershipScore),
          availabilityScoreBreakDown = Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(0.5), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          ownershipScoreBreakDown = None
        )
      case _ =>  //This case can only be triggered via direct call to ai-gateway-service but not via ID+
        val mbStatusScore = scorer.getMbOnlyAvailabilityScore(availabilityResult.mbDecision, availabilityResult.mbLastSeenMulti, availabilityResult.mbReturns)
        val triceStatusScore = scorer.getTriceAvailabilityScore(availabilityResult.triceAsvDecision)
        val convlStatusScore = scorer.getConvlAvailabilityScore(availabilityResult.convlStatus)
//        val globalStatusScore = getGlobalStatusScore(request, availabilityResult, mbStatusScore, triceStatusScore, convlStatusScore)
//        val ownershipScore = scorer.getOwnershipScore(ownershipResult.get, request.params.businessName)
        AIGatewayScore(
          availabilityScore = None, //Some(globalStatusScore),
          ownershipScore = None, //Some(ownershipScore),
          availabilityScoreBreakDown = Some(AIGatewayScoreBreakdown(mbltScore = Some(mbStatusScore), bnymScore = Some(0.5), triceScore = Some(triceStatusScore), convlScore = Some(convlStatusScore))),
          ownershipScoreBreakDown = None
        )
    }
  }

  def getGlobalStatusScore(request: AIGatewayRequest, availabilityResult: MbOnlyAvailabilityResult, mbStatusScore: Double, triceStatusScore: Double, convlStatusScore: Double) = {
    implicit val trxId = TrxId(request.metadata.transactionId)
    scorer.getGlobalAvailabilityScoreWithConsortium(triceStatusScore, 0.5, convlStatusScore, mbStatusScore)
  }

  def getGlobalStatusScore(request: AIGatewayRequest, availabilityResult: AvailabilityResult, mbStatusScore: Double, triceStatusScore: Double, bnymStatusScore: Double, convlStatusScore: Double) = {
    implicit val trxId = TrxId(request.metadata.transactionId)
    scorer.getGlobalAvailabilityScoreWithConsortium(triceStatusScore, bnymStatusScore, convlStatusScore, mbStatusScore)
  }

  private def updateInquiries(inquiries: Set[String]): Set[String] = {
    val updatedInquiries = if (inquiries.contains(AIGatewayInquiries.STATUS)) {
      inquiries.filterNot(_.equalsIgnoreCase(AIGatewayInquiries.STATUS)) ++ Set(AIGatewayInquiries.AVAILABILITY)
    } else inquiries
    updatedInquiries
  }

  private def extractDecimalPartAsNumber(value: Double): Int = {
    val fractionalPart = value - value.toInt
    val scaled = BigDecimal(fractionalPart).setScale(2, BigDecimal.RoundingMode.HALF_UP).toDouble
    (scaled * 100).toInt
  }

}

object RuleCodeService {
  val WARNING = "Warning"
  val MATCH = "Match"
  val ALERT = "Alert"
  val UNKNOWN = "Unknown"
  val NO_MATCH = "No Match"
  val refMap = Map(MATCH -> "Yes",
    WARNING -> "No",
    UNKNOWN -> "Unknown",
    ALERT -> "Conditional",
    NO_MATCH -> "No"
  )
  val BNYVL_SSN = "BNYVL_100008"
  val BNYVL_DOB = "BNYVL_100009"
  val BNYVL_NAME = "BNYVL"
}
