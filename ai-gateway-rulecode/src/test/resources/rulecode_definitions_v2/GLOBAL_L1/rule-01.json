{"version": "v2", "vendor_variables": [{"variable_name": "vendor_vars", "operators": [{"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100007"}], "output": "bnyvl_bus_name_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100018"}], "output": "bnyvl_address_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100009"}], "output": "bnyvl_dob_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100002"}], "output": "bnyvl_asv_description"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100009"}], "output": "mbtvl_property_message"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100010"}], "output": "mbtvl_returns_thr"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100011"}], "output": "mbtvl_last_seen_multi_thr"}]}], "rules": [{"rule_code_name": "GLOBAL.300870", "description": "GLOBAL_SAI_ROUTING_NUMBER_INVALID", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.bnyvl_asv_description"], "methods": ["clean_string", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___invalid abart__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bnyvl_invalid_routing_rulecode"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["clean_string", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___routing number is invalid__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_invalid_routing_rulecode"}, {"name": "if_else", "preprocessors": [], "inputs": ["bnyvl_invalid_routing_rulecode", "mbtvl_invalid_routing_rulecode", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "invalid_routing_rulecode"}]}, {"rule_code_name": "GLOBAL.300872", "description": "GLOBAL_ACCOUNT_WARNING", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["clean_string", "trim"], "output": "clean_property_message"}], "inputs": ["clean_property_message", "___history of returns no current unpaid items__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_property_message_unpaid"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["trim", "clean_string"], "output": "clean_property_message"}], "inputs": ["clean_property_message", "___neg__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_property_message_neg"}, {"name": "if_else", "inputs": ["mbtvl_property_message_unpaid", "mbtvl_property_message_neg", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "global_account_warning_rulecode"}]}, {"rule_code_name": "GLOBAL.300878", "description": "GLOBAL_LAST_SEEN_THRESHOLD_15", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y15__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_15_rulecode"}]}, {"rule_code_name": "GLOBAL.300879", "description": "GLOBAL_LAST_SEEN_THRESHOLD_30", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y30__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_30_rulecode"}]}, {"rule_code_name": "GLOBAL.300880", "description": "GLOBAL_LAST_SEEN_THRESHOLD_45", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y45__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_45_rulecode"}]}, {"rule_code_name": "GLOBAL.300881", "description": "GLOBAL_LAST_SEEN_THRESHOLD_60", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y60__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_60_rulecode"}]}, {"rule_code_name": "GLOBAL.300882", "description": "GLOBAL_LAST_SEEN_THRESHOLD_75", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y75__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_75_rulecode"}]}, {"rule_code_name": "GLOBAL.300883", "description": "GLOBAL_LAST_SEEN_THRESHOLD_90", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y90__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_90_rulecode"}]}, {"rule_code_name": "GLOBAL.300884", "description": "GLOBAL_LAST_SEEN_THRESHOLD_120", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y120__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_120_rulecode"}]}, {"rule_code_name": "GLOBAL.300885", "description": "GLOBAL_LAST_SEEN_THRESHOLD_150", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y150__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_150_rulecode"}]}, {"rule_code_name": "GLOBAL.300886", "description": "GLOBAL_LAST_SEEN_THRESHOLD_180", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y180__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_180_rulecode"}]}, {"rule_code_name": "GLOBAL.300887", "description": "GLOBAL_LAST_SEEN_THRESHOLD_210", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y210__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_210_rulecode"}]}, {"rule_code_name": "GLOBAL.300888", "description": "GLOBAL_LAST_SEEN_THRESHOLD_240", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y240__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_240_rulecode"}]}, {"rule_code_name": "GLOBAL.300889", "description": "GLOBAL_LAST_SEEN_THRESHOLD_270", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y270__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_270_rulecode"}]}, {"rule_code_name": "GLOBAL.300892", "description": "GLOBAL_LAST_SEEN_THRESHOLD_300", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y300__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_300_rulecode"}]}, {"rule_code_name": "GLOBAL.300893", "description": "GLOBAL_LAST_SEEN_THRESHOLD_365", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y365__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_365_rulecode"}]}, {"rule_code_name": "GLOBAL.300894", "description": "GLOBAL_LAST_SEEN_THRESHOLD_730", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y400__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_400"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y450__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_450"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y500__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_500"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y600__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_600"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y730__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_730"}, {"name": "if_else", "preprocessors": [], "inputs": ["global_last_seen_multi_400", "global_last_seen_multi_450", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "temp_block_1"}, {"name": "if_else", "preprocessors": [], "inputs": ["temp_block_1", "global_last_seen_multi_500", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "temp_block_2"}, {"name": "if_else", "preprocessors": [], "inputs": ["temp_block_2", "global_last_seen_multi_600", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "temp_block_3"}, {"name": "if_else", "preprocessors": [], "inputs": ["temp_block_3", "global_last_seen_multi_730", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "temp_block_4"}, {"name": "if_else", "preprocessors": [], "inputs": ["temp_block_4", "___true__as_boolean__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "boolean"}], "output": "global_last_seen_multi_730_rulecode"}]}, {"rule_code_name": "GLOBAL.300895", "description": "GLOBAL_LAST_SEEN_THRESHOLD_Y", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y900__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_900"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_last_seen_multi_thr"], "methods": ["trim", "lowercase"], "output": "clean_multi_thr"}], "inputs": ["clean_multi_thr", "___y__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_last_seen_multi_y"}, {"name": "if_else", "preprocessors": [], "inputs": ["global_last_seen_multi_900", "global_last_seen_multi_y", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "temp_block"}, {"name": "if_else", "preprocessors": [], "inputs": ["temp_block", "___true__as_boolean__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "boolean"}], "output": "global_last_seen_y_rulecode"}]}, {"rule_code_name": "GLOBAL.300891", "description": "GLOBAL_RETURN_HISTORY_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_returns_thr"], "methods": ["trim", "lowercase"], "output": "clean_returns_thr"}], "inputs": ["clean_returns_thr", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_return_history_rulecode"}]}]}