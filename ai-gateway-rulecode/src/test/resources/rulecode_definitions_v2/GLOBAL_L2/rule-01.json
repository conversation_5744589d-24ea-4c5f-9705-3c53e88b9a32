{"version": "v2", "vendor_variables": [{"variable_name": "global_vars", "operators": [{"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300801"}], "output": "global_fname_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300802"}], "output": "global_lname_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300803"}], "output": "global_bus_name_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300804"}], "output": "global_phone_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300805"}], "output": "global_address_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300806"}], "output": "global_ssn_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300807"}], "output": "global_dob_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300808"}], "output": "global_asv_status_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.numericalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300809"}], "output": "global_asv_score"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300811"}], "output": "global_asv_pattern"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100008"}], "output": "bnyvl_ssn_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300813"}], "output": "socid_ssn_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100009"}], "output": "bnyvl_dob_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300814"}], "output": "socid_dob_rulecode"}]}, {"variable_name": "unknown_asv_patterns", "operators": [{"name": "lookup", "preprocessors": [{"methods": ["split"], "inputs": ["___UNKNOWN_U_N_Y,UNKNOWN_U_N_N,UNKNOWN_U_Y_N,UNKNOWN_AS_Y_N,UNKNOWN_AS_N_Y,UNKNOWN_AS_N_N,U_N_No,AS_N_No,D_N_No", "___,"], "output": "unknown_asv_patterns_arr"}], "inputs": ["unknown_asv_patterns_arr"], "options": [], "output": "unknown_asv_patterns_arr"}]}], "rules": [{"rule_code_name": "GLOBAL.300820", "description": "GLOBAL_SAI_FNAME_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes"], "options": [{"name": "attr", "value": "GLOBAL_300801"}], "output": "global_fname_rulecode"}, {"name": "if_else", "preprocessors": [{"inputs": ["global_fname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "first_name_match_yes"}]}, {"rule_code_name": "GLOBAL.300821", "description": "GLOBAL_SAI_FNAME_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_fname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "first_name_match_no"}]}, {"rule_code_name": "GLOBAL.300822", "description": "GLOBAL_SAI_FNAME_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_fname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "first_name_match_conditional"}]}, {"rule_code_name": "GLOBAL.300824", "description": "GLOBAL_SAI_LNAME_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_lname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "last_name_match_yes"}]}, {"rule_code_name": "GLOBAL.300825", "description": "GLOBAL_SAI_LNAME_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_lname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "last_name_match_no"}]}, {"rule_code_name": "GLOBAL.300826", "description": "GLOBAL_SAI_LNAME_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_lname_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "last_name_match_conditional"}]}, {"rule_code_name": "GLOBAL.300828", "description": "GLOBAL_SAI_BUSNAME_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_bus_name_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bus_name_match_yes"}]}, {"rule_code_name": "GLOBAL.300829", "description": "GLOBAL_SAI_BUSNAME_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_bus_name_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bus_name_match_no"}]}, {"rule_code_name": "GLOBAL.300830", "description": "GLOBAL_SAI_BUSNAME_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_bus_name_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bus_name_match_conditional"}]}, {"rule_code_name": "GLOBAL.300832", "description": "GLOBAL_SAI_DOB_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_dob_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "dob_match_yes"}]}, {"rule_code_name": "GLOBAL.300833", "description": "GLOBAL_SAI_DOB_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_dob_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "dob_match_no"}]}, {"rule_code_name": "GLOBAL.300834", "description": "GLOBAL_SAI_DOB_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_dob_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "dob_match_conditional"}]}, {"rule_code_name": "GLOBAL.300836", "description": "GLOBAL_SAI_SSN_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_ssn_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "ssn_match_yes"}]}, {"rule_code_name": "GLOBAL.300837", "description": "GLOBAL_SAI_SSN_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_ssn_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "ssn_match_no"}]}, {"rule_code_name": "GLOBAL.300838", "description": "GLOBAL_SAI_SSN_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_ssn_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "ssn_match_conditional"}]}, {"rule_code_name": "GLOBAL.300840", "description": "GLOBAL_SAI_ADDRESS_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_address_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "address_match_yes"}]}, {"rule_code_name": "GLOBAL.300841", "description": "GLOBAL_SAI_ADDRESS_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_address_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "address_match_no"}]}, {"rule_code_name": "GLOBAL.300842", "description": "GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_address_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "address_match_conditional"}]}, {"rule_code_name": "GLOBAL.300844", "description": "GLOBAL_SAI_PHONE_MATCH_YES", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_phone_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "phone_match_yes"}]}, {"rule_code_name": "GLOBAL.300845", "description": "GLOBAL_SAI_PHONE_MATCH_NO", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_phone_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___no__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "phone_match_no"}]}, {"rule_code_name": "GLOBAL.300846", "description": "GLOBAL_SAI_PHONE_MATCH_CONDITIONAL", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.global_phone_rulecode"], "methods": ["lowercase", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___conditional__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "phone_match_conditional"}]}, {"rule_code_name": "GLOBAL.300873", "description": "GLOBAL_ACCOUNT_NO_INFO", "type": "numerical", "default": "NA", "operators": [{"name": "contains", "inputs": ["vendor.unknown_asv_patterns.unknown_asv_patterns_arr", "vendor.global_vars.global_asv_pattern"], "options": [{"name": "condition", "value": "any"}], "output": "pattern_unknown"}, {"name": "if_else", "inputs": ["pattern_unknown", "___true__as_boolean__", "___1__as_int__", "_____as_<PERSON>A__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "boolean"}], "output": "asv_no_info"}]}, {"rule_code_name": "GLOBAL.300874", "description": "GLOBAL_SSN_DERIVED_FROM_SOCUREID", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.bnyvl_ssn_rulecode"], "methods": ["clean_string", "lowercase", "trim"], "output": "clean_local_ssn_rule_code"}], "inputs": ["clean_local_ssn_rule_code", "___unknown__as_string__", "___true__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bnyvl_ssn_unknown"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.socid_ssn_rulecode"], "methods": ["clean_string", "lowercase", "trim"], "output": "clean_local_ssn_rule_code"}], "inputs": ["clean_local_ssn_rule_code", "___unknown__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "!="}, {"name": "dataType", "value": "string"}], "output": "socid_ssn_not_unknown"}, {"name": "if_else", "preprocessors": [], "inputs": ["bnyvl_ssn_unknown", "socid_ssn_not_unknown", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "&&"}, {"name": "dataType", "value": "boolean"}], "output": "ssn_derived_from_socid"}]}, {"rule_code_name": "GLOBAL.300875", "description": "GLOBAL_DOB_DERIVED_FROM_SOCUREID", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.bnyvl_dob_rulecode"], "methods": ["clean_string", "trim"], "output": "clean_local_dob_rule_code"}], "inputs": ["clean_local_dob_rule_code", "___unknown__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___true__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bnyvl_dob_unknown"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.global_vars.socid_dob_rulecode"], "methods": ["clean_string", "trim"], "output": "clean_local_dob_rule_code"}], "inputs": ["clean_local_dob_rule_code", "___unknown__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "!="}, {"name": "dataType", "value": "string"}], "output": "socid_dob_not_unknown"}, {"name": "if_else", "preprocessors": [], "inputs": ["bnyvl_dob_unknown", "socid_dob_not_unknown", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "&&"}, {"name": "dataType", "value": "boolean"}], "output": "dob_derived_from_socid"}]}, {"rule_code_name": "GLOBAL.300877", "description": "GLOBAL_DECEASED_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "lookup", "inputs": ["input.ruleCodes.numericalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300810"}], "output": "global_ownership_score"}, {"name": "lookup", "inputs": ["input.ruleCodes.numericalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300897"}], "output": "global_ssn_deceased_check"}, {"name": "lookup", "inputs": ["input.ruleCodes.numericalRulecodes_optional_"], "options": [{"name": "attr", "value": "GLOBAL_300898"}], "output": "global_vrval_identity_is_deceased"}, {"name": "if_else", "inputs": ["global_ownership_score", "___0.5__as_double__", "___1__as_int__", "___0__as_int__", "___0__as_int__"], "options": [{"name": "condition", "value": "!="}, {"name": "dataType", "value": "double"}], "output": "global_deceased_indicator"}, {"name": "if_else", "inputs": ["global_ssn_deceased_check", "___1.0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "double"}], "output": "vrval_ssn_deceased"}, {"name": "if_else", "inputs": ["global_vrval_identity_is_deceased", "___1.0__as_double__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "double"}], "output": "vrval_identity_is_deceased"}, {"name": "if_else", "inputs": ["vrval_ssn_deceased", "vrval_identity_is_deceased", "global_deceased_indicator", "___0__as_int__", "___0__as_int__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "global_deceased_indicator"}]}, {"rule_code_name": "GLOBAL.300905", "description": "PRIMARY_FAILURE_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.trice_asv_decision"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_asv_decision"}], "inputs": ["clean_asv_decision", "___failed__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "primary_failure_indicator"}]}, {"rule_code_name": "GLOBAL.300906", "description": "ACCOUNT_CLOSED_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.trice_account_status"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_trice_status"}], "inputs": ["clean_trice_status", "___bank account closed__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "trice_account_closed"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.bnyvl_asv_description"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_bnyvl_status"}], "inputs": ["clean_bnyvl_status", "___account terminated__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bnyvl_account_closed"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.convl_account_status"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_convl_status"}], "inputs": ["clean_convl_status", "___closed__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "convl_account_closed"}, {"name": "if_else", "inputs": ["trice_account_closed", "bnyvl_account_closed", "convl_account_closed", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "account_closed_indicator"}]}, {"rule_code_name": "GLOBAL.300907", "description": "ACCOUNT_BLOCKED_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.trice_account_status"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_trice_status"}], "inputs": ["clean_trice_status", "___bank account blocked__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "account_blocked_indicator"}]}, {"rule_code_name": "GLOBAL.300908", "description": "INVALID_ACCOUNT_NUMBER_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.trice_account_status"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_trice_status"}], "inputs": ["clean_trice_status", "___creditor account number invalid or missing__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "trice_invalid_account"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_account_number_status"], "methods": ["clean_string", "trim", "lowercase"], "output": "clean_mbtvl_status"}], "inputs": ["clean_mbtvl_status", "___account number is invalid__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_invalid_account"}, {"name": "if_else", "inputs": ["trice_invalid_account", "mbtvl_invalid_account", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "invalid_account_number_indicator"}]}]}