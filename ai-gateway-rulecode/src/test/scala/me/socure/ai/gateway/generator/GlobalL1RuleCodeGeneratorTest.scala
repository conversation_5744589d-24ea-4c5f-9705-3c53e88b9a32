package me.socure.ai.gateway.generator

import me.socure.ai.gateway.common.models.{AIGatewayGlobalRulecodes, AIGatewayRuleCodes, RulecodeRequest}
import net.spy.memcached.compat.log.LoggerFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FunSuite, Matchers}

class GlobalL1RuleCodeGeneratorTest extends FunSuite with Matchers with ScalaFutures {
  private val logger = LoggerFactory.getLogger(this.getClass)
  private val TransactionId = "test-transaction-id"
  private val AccountId = 1234

  test("Threshold Global Test Case") {
    val TestCases: Seq[(Option[String], Option[String], Option[(String, Double)])] = Seq(
      //TRICE.100005, MBTVL.100011, Expected Global
      (Some("accepted"), Some("Y15"), Some("GLOBAL.300878" -> 1.0)),
      (Some("accepted"), Some("Y"), Some("GLOBAL.300878" -> 1.0)),
      (Some("accepted"), Some("Y45"), Some("GLOBAL.300878" -> 1.0)),
      (None, Some("Y60"), Some("GLOBAL.300881" -> 1.0)),
      (None, Some("Y365"), Some("GLOBAL.300893" -> 1.0)),
      (None, Some("Y400"), Some("GLOBAL.300894" -> 1.0)),
      (None, Some("Y450"), Some("GLOBAL.300894" -> 1.0)),
      (None, Some("Y500"), Some("GLOBAL.300894" -> 1.0)),
      (None, Some("Y600"), Some("GLOBAL.300894" -> 1.0)),
      (None, Some("Y730"), Some("GLOBAL.300894" -> 1.0)),
      (None, Some("Y900"), Some("GLOBAL.300895" -> 1.0)),
      (None, Some("Y"), Some("GLOBAL.300895" -> 1.0)),
      (None, None, None)
    )

    TestCases.foreach((testCase) => {
      logger.info("Test Case - TRICE.100005, MBTVL.100011, Expected")
      logger.info(s"${testCase._1}, ${testCase._2}, ${testCase._3}")

      val inputCategoricals: Map[String, String] = (testCase._1, testCase._2) match {
        case (Some(triceValue), Some(mbtvlValue)) =>
          Map(
            "TRICE_100005" -> triceValue,
            "MBTVL_100011" -> mbtvlValue
          )
        case (Some(triceValue), None) =>
          Map(
            "TRICE_100005" -> triceValue
          )
        case (None, Some(mbtvlValue)) =>
          Map(
            "MBTVL_100011" -> mbtvlValue
          )
        case (None, None) =>
          Map.empty
      }

      val actual = GlobalL1RuleCodeGenerator.generateThresholdRuleCode(
        RulecodeRequest(
          ruleCodes = AIGatewayRuleCodes(
            numericalRulecodes = Map.empty,
            categoricalRulecodes = inputCategoricals
          ),
          transactionId = TransactionId,
          accountId = AccountId,
          vendor = GlobalL1RuleCodeGenerator.Vendor),
        codeRuleCodes = AIGatewayRuleCodes(Map.empty, Map.empty),
        l1RuleCodes = AIGatewayGlobalRulecodes("", Map.empty, Map.empty),
        transactionId = TransactionId
      )
      logger.info(s"Actual - ${actual}")
      actual shouldBe testCase._3
    })
  }
}
