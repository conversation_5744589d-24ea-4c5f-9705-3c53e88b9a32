package me.socure.ai.gateway.rulecode

import com.github.tototoshi.csv.CSVReader
import me.socure.ai.gateway.common.models.vendor._
import me.socure.ai.gateway.common.models.{AIGatewayGlobalRulecodes, AIGatewayMetadata, AIGatewayParams, AIGatewayPayment, AIGatewayRequest, AIGatewayVendorConfig}
import me.socure.ai.gateway.common.util.Scorer
import me.socure.common.clock.RealClock
import me.socure.common.config.{InputOnlyRuleCodeConfigurationResolver, RuleCodeInputResolverImpl}
import me.socure.common.generator.RuleCodeGeneratorV2
import me.socure.common.resolver.RuleCodeDependencyResolver
import me.socure.common.utility.{ContextCache, GuavaContextCache}
import org.json4s.JsonAST.JString
import org.json4s.jackson.JsonMethods.parse
import org.json4s.{DefaultFormats, Formats, JValue}
import org.scalatest.{FreeSpec, Matchers}

import java.io.InputStreamReader
import java.util.concurrent.atomic.AtomicReference
import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

class RuleCodeServiceTest extends FreeSpec with Matchers {

  private implicit val formats: Formats = DefaultFormats

  val contextCache: ContextCache[String, Map[String, JValue]] = new GuavaContextCache(100, 10)
  val defaultRuleCodeConfigurationResolver = new InputOnlyRuleCodeConfigurationResolver

  val ruleCodeDependencyResolver = new RuleCodeDependencyResolver(
    ruleCodeDao = null,
    ruleCodeHttpPlugin = null,
    ruleCodeDataFileLookup = null,
    tpAuditClient = null,
    clock = new RealClock
  )
  val ruleCodeInputResolverImpl = new RuleCodeInputResolverImpl
  val ruleCodeGeneratorV2 = new RuleCodeGeneratorV2(contextCache = contextCache,
    ruleCodeDependencyResolver = ruleCodeDependencyResolver,
    ruleCodeInputResolver = ruleCodeInputResolverImpl)

  System.setProperty("APP_ENVIRONMENT", "test")
  System.setProperty("CONFIGURATION_NAME", "test")

  val ruleCodeConfigurationV2 = defaultRuleCodeConfigurationResolver.resolveV2()
  var rconfig = new AtomicReference(ruleCodeConfigurationV2)
  var scorer = new Scorer
  val rulecodeService = new RuleCodeService(ruleCodeGeneratorV2, rconfig, scorer)

  def aiPremierGatewayRequest: JValue = {
    val inputJson =
      s"""{
         |    "metadata": {
         |        "accountId": 2277,
         |        "environmentTypeId": 1,
         |        "maskPii": false,
         |        "transactionId": "abcd-123"
         |    },
         |    "payment": {
         |        "accountNumber": "89455",
         |        "routingNumber": "*********",
         |        "inquiries": ["AVAILABILITY","OWNERSHIP"]
         |    },
         |    "params": {
         |        "firstName": "Wilson",
         |        "surName": "Paul",
         |        "physicalAddress": "206 GOODWIN ST",
         |        "city": "MERRIT",
         |        "nationalId": "*********",
         |        "state": "MI",
         |        "zip": "49667"
         |    },
         |    "vendors": [
         |        {
         |            "name": "BNYVL",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "MBTVL",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "SOCVL",
         |            "timeoutInMillis": 1200
         |        }
         |    ]
         |}""".stripMargin

    parse(inputJson)
  }

  def aiGatewayBusinessRequest: JValue = {
    val inputJson =
      s"""{
         |    "metadata": {
         |        "accountId": 2277,
         |        "environmentTypeId": 1,
         |        "maskPii": false,
         |        "transactionId": "abcd-123"
         |    },
         |    "payment": {
         |        "accountNumber": "***********",
         |        "routingNumber": "*********",
         |        "inquiries": ["AVAILABILITY","OWNERSHIP"]
         |    },
         |    "params": {
         |        "businessName": "COOLING",
         |        "businessPhone": "**********",
         |        "physicalAddress": "1644 E. GLENDALE AVE. SUITE 1",
         |        "city": "PHOENIX",
         |        "state": "AZ",
         |        "zip": "85021"
         |    },
         |    "vendors": [
         |        {
         |            "name": "BNYVL",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "MBTVL",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "SOCVL",
         |            "timeoutInMillis": 1200
         |        }
         |    ]
         |}""".stripMargin

    parse(inputJson)
  }

  def aiGatewayRequest: JValue = {
    val inputJson =
      s"""{
         |    "metadata": {
         |        "accountId": 2277,
         |        "environmentTypeId": 1,
         |        "maskPii": false,
         |        "transactionId": "abcd-123"
         |    },
         |    "payment": {
         |        "accountNumber": "89455",
         |        "routingNumber": "*********",
         |        "accountCountry": "US",
         |        "inquiries": ["AVAILABILITY","OWNERSHIP"]
         |    },
         |    "params": {
         |        "firstName": "Wilson",
         |        "surName": "Paul",
         |        "physicalAddress": "206 GOODWIN ST",
         |        "city": "MERRIT",
         |        "nationalId": "*********",
         |        "state": "MI",
         |        "zip": "49667"
         |    },
         |    "vendors": [
         |        {
         |            "name": "MBTVL",
         |            "timeoutInMillis": 1200
         |        },
         |        {
         |            "name": "SOCVL",
         |            "timeoutInMillis": 1200
         |        }
         |    ]
         |}""".stripMargin

    parse(inputJson)
  }

  "test global rulecodes" in {
    val stream = getClass.getResourceAsStream("/test_data/rulecode_test_data.csv")
    val reader = new InputStreamReader(stream)
    val csvReader = CSVReader.open(reader)

    csvReader.toStreamWithHeaders
      .foreach(unfilteredRow => {
        //Out of the csv row that is being read filter all keys that do not have a value
        val row = unfilteredRow.filter(!_._2.equals(""))
        val bnyCategoricalRuleCodes = Map(
          "BNYVL.100001" -> row.getOrElse("BNYM_ASV_STATUS_CODE", ""),
          "BNYVL.100002" -> row.getOrElse("BNYM_ASV_DESCRIPTION", ""),
          "BNYVL.100003" -> row.getOrElse("BNYM_AOV_STATUS_CODE", ""),
          "BNYVL.100004" -> row.getOrElse("BNYM_FNAME", ""),
          "BNYVL.100005" -> row.getOrElse("BNYM_LNAME", ""),
          "BNYVL.100006" -> row.getOrElse("BNYM_NAME", ""),
          "BNYVL.100007" -> row.getOrElse("BNYM_BUSNAME", ""),
          "BNYVL.100008" -> row.getOrElse("BNYM_SSN", ""),
          "BNYVL.100009" -> row.getOrElse("BNYM_DOB", ""),
          "BNYVL.100010" -> row.getOrElse("BNYM_ADDRESS", ""),
          "BNYVL.100011" -> row.getOrElse("BNYM_CITY", ""),
          "BNYVL.100012" -> row.getOrElse("BNYM_STATE", ""),
          "BNYVL.100013" -> row.getOrElse("BNYM_ZIP", ""),
          "BNYVL.100014" -> row.getOrElse("BNYM_HMPHONE", ""),
          "BNYVL.100016" -> row.getOrElse("BNYM_FNAME_MATCH", ""),
          "BNYVL.100017" -> row.getOrElse("BNYM_LNAME_MATCH", ""),
          "BNYVL.100018" -> row.getOrElse("BNYM_ADDRESS_MATCH", ""),
          "BNYVL.100020" -> row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""),
          "BNYVL.100021" -> row.getOrElse("BNYM_WKPHONE", ""),
          "BNYVL.100022" -> row.getOrElse("BNYM_PHONE", "")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredBnyCategoricalRuleCodes = bnyCategoricalRuleCodes.filter(!_._2.equals(""))

        val bnyMellonMetadata: AIVendorMetadata = new AIVendorMetadata("BNYMELLON", true, "COMPLETED", "7dac7147-c42f-44a6-a3b4-8bae8844c2a0", "22292716e8b59e8")
        val bnyOwnership: Option[AccountOwnership] = Some(AccountOwnership("Match", "Match: CITY,FNAME,LNAME,NAME,STATE,ZIP; 100"))
        val bnyStatus: Option[AccountStatus] = Some(AccountStatus(row.getOrElse("BNYM_ASV_STATUS_CODE", ""), row.getOrElse("BNYM_ASV_DESCRIPTION", ""), None, Some(""), Some(row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""))))
        val bnyResult: AIVendorResult = AIVendorResult(bnyOwnership, bnyStatus)


        val aiBnyMellonResponseMock: AIVendorResponse = new AIVendorResponse(bnyMellonMetadata, bnyResult, None, Some(new AIVendorRuleCodes(Map(), filteredBnyCategoricalRuleCodes)))

        val mbtCategoricalRuleCodes = Map(
          "MBTVL.100001" -> row.getOrElse("MBT_FNAME_MATCH", ""),
          "MBTVL.100002" -> row.getOrElse("MBT_LNAME_MATCH", ""),
          "MBTVL.100003" -> row.getOrElse("MBT_PHONE_MATCH", ""),
          "MBTVL.100004" -> row.getOrElse("MBT_DECISION_CODE", ""),
          "MBTVL.100005" -> row.getOrElse("MBT_LAST_SEEN_THR", ""),
          "MBTVL.100008" -> row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""),
          "MBTVL.100009" -> row.getOrElse("MBT_PROPERTY_MESSAGE_SIMP", ""),
          "MBTVL.100010" -> row.getOrElse("MBT_RETURNS_THR", ""),
          "MBTVL.100011" -> row.getOrElse("MBT_LAST_SEEN_MULTI_THR", "")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredMbtCategoricalRuleCodes = mbtCategoricalRuleCodes.filter(!_._2.equals(""))

        val mbtMetadata: AIVendorMetadata = new AIVendorMetadata("MICROBILT", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val mbtStatus: Option[AccountStatus] = Some(AccountStatus("A", row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""), Some(row.getOrElse("MBT_DECISION_CODE", "")), Some(row.getOrElse("MBT_LAST_SEEN_THR", "")), None))
        val mbtResult: AIVendorResult = AIVendorResult(None, mbtStatus)

        val aiMbtResponseMock: AIVendorResponse = new AIVendorResponse(mbtMetadata, mbtResult, None, Some(new AIVendorRuleCodes(Map(), filteredMbtCategoricalRuleCodes)))

        //SOCID_FNAME_MATCH_FUZZY,SOCID_LNAME_MATCH_FUZZY,SOCID_SSN,SOCID_DOB,SOCID_MATCH_SCORE,GLOBAL_SAI_SOCID_SSN_MATCH,GLOBAL_SAI_SOCID_DOB_MATCH,GLOBAL_SSN_DERIVED_FROM_SOCUREID,GLOBAL_DOB_DERIVED_FROM_SOCUREID
        val socIdCategoricalRuleCodes = Map(
          "SOCVL.100001" -> row.getOrElse("SOCID_FNAME_MATCH_FUZZY", ""),
          "SOCVL.100002" -> row.getOrElse("SOCID_LNAME_MATCH_FUZZY", ""),
          "SOCVL.100003" -> row.getOrElse("SOCID_SSN", ""),
          "SOCVL.100004" -> row.getOrElse("SOCID_DOB", ""),
          "SOCVL.100006" -> row.getOrElse("SOCID_DECEASED", "")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredSocIdCategoricalRuleCodes = socIdCategoricalRuleCodes.filter(!_._2.equals(""))

        val socIdNumericalRuleCodes: Map[String, Double] = Map(
          "SOCVL.100005" -> row.getOrElse("SOCID_MATCH_SCORE", "0.0").toDouble
        )

        val socIdMetadata: AIVendorMetadata = new AIVendorMetadata("SOCID", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val socIdStatus: Option[AccountStatus] = Some(AccountStatus("A", "", Some(""), Some(""), None))
        val socIdResult: AIVendorResult = AIVendorResult(None, socIdStatus)

        val socIdResponseMock: AIVendorResponse = new AIVendorResponse(socIdMetadata, socIdResult, None, Some(new AIVendorRuleCodes(socIdNumericalRuleCodes, filteredSocIdCategoricalRuleCodes)))

        var aiGatewayRequestJValue = aiPremierGatewayRequest.replace("payment" :: "accountNumber" :: Nil, JString(row.getOrElse("accountNumber", "*********")))
        aiGatewayRequestJValue = aiGatewayRequestJValue.replace("payment" :: "routingNumber" :: Nil, JString(row.getOrElse("routingNumber", "1234556")))

        val result = rulecodeService.computeVendorRulecodes("f7ec6b82-e0f8-45e2-b0cd-7b17d6721f5d", 1234,
          Seq(aiBnyMellonResponseMock, aiMbtResponseMock, socIdResponseMock),
          aiGatewayRequestJValue.extract[AIGatewayRequest])

        val ruleCodeResponse = Await.result(result, Duration.Inf)

        val numericalRulecodes = ruleCodeResponse.get.numericalRulecodes
        val categoricalRulecodes = ruleCodeResponse.get.categoricalRulecodes

        assertResult(row.get("GLOBAL_SAI_FNAME_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300801")
        }
        assertResult(row.get("GLOBAL_SAI_LNAME_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300802")
        }
        assertResult(row.get("GLOBAL_SAI_PHONE_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300804")
        }
        assertResult(row.get("GLOBAL_SAI_ADDRESS_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300805")
        }
        assertResult(row.get("GLOBAL_SAI_SSN_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300806")
        }
        assertResult(row.get("GLOBAL_SAI_DOB_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300807")
        }
//        assertResult(row("GLOBAL_SAI_ASV_SCORE").toDouble) {
//          numericalRulecodes("GLOBAL.300809")
//        }
        assertResult(row("GLOBAL_SAI_AOV_SCORE").toDouble) {
          numericalRulecodes("GLOBAL.300810")
        }

//        assertResult(row.get("GLOBAL_SAI_ASV_PATTERN")) {
//          categoricalRulecodes.get("GLOBAL.300811")
//        }

        assertResult(row.get("GLOBAL_SAI_AOV_PATTERN")) {
          categoricalRulecodes.get("GLOBAL.300812")
        }

        // Fname Numerical
        if (row.contains("GLOBAL_SAI_FNAME_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_FNAME_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300820")
          }
        }
        else if (row.contains("GLOBAL_SAI_FNAME_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_FNAME_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300821")
          }
        }
        else if (row.contains("GLOBAL_SAI_FNAME_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_FNAME_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300822")
          }
        }

        //Lname numerical
        if (row.contains("GLOBAL_SAI_LNAME_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_LNAME_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300824")
          }
        }
        else if (row.contains("GLOBAL_SAI_LNAME_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_LNAME_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300825")
          }
        }
        else if (row.contains("GLOBAL_SAI_LNAME_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_LNAME_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300826")
          }
        }


        //Address Numerical
        if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300840")
          }
        }
        else if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300841")
          }
        }
        else if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300842")
          }
        }

        //Phone Numerical
        if (row.contains("GLOBAL_SAI_PHONE_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300844")
          }
        }
        else if (row.contains("GLOBAL_SAI_PHONE_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300845")
          }
        }
        else if (row.contains("GLOBAL_SAI_PHONE_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300846")
          }
        }

        //Dob Numerical
        if (row.contains("GLOBAL_SAI_DOB_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_DOB_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300832")
          }
        }
        else if (row.contains("GLOBAL_SAI_DOB_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_DOB_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300833")
          }
        }
        else if (row.contains("GLOBAL_SAI_DOB_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_DOB_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300834")
          }
        }

        // Ssn Numerical
        if (row.contains("GLOBAL_SAI_SSN_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_SSN_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300836")
          }
        }
        else if (row.contains("GLOBAL_SAI_SSN_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_SSN_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300837")
          }
        }
        else if (row.contains("GLOBAL_SAI_SSN_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_SSN_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300838")
          }
        }


        if (row.contains("GLOBAL_SAI_ROUTING_NUMBER_INVALID")) {
          assertResult(row("GLOBAL_SAI_ROUTING_NUMBER_INVALID").toDouble) {
            numericalRulecodes("GLOBAL.300870")
          }
        }

        if (row.contains("GLOBAL_ACCOUNT_WARNING")) {
          assertResult(row("GLOBAL_ACCOUNT_WARNING").toDouble) {
            numericalRulecodes("GLOBAL.300872")
          }
        }

        if (row.contains("GLOBAL_ACCOUNT_NO_INFO")) {
          assertResult(row("GLOBAL_ACCOUNT_NO_INFO").toDouble) {
            numericalRulecodes("GLOBAL.300873")
          }
        }

        if (row.contains("GLOBAL_ACC_STRUCTURE_ISSUE")) {
          assertResult(row("GLOBAL_ACC_STRUCTURE_ISSUE").toDouble) {
            numericalRulecodes("GLOBAL.300876")
          }
        }

        if (row.contains("GLOBAL_SSN_DERIVED_FROM_SOCUREID")) {
          assertResult(row("GLOBAL_SSN_DERIVED_FROM_SOCUREID").toDouble) {
            numericalRulecodes("GLOBAL.300874")
          }
        }

        if (row.contains("GLOBAL_DOB_DERIVED_FROM_SOCUREID")) {
          assertResult(row("GLOBAL_DOB_DERIVED_FROM_SOCUREID").toDouble) {
            numericalRulecodes("GLOBAL.300875")
          }
        }

        if (row.contains("GLOBAL_DECEASED_INDICATOR")) {
          assertResult(row("GLOBAL_DECEASED_INDICATOR").toDouble) {
            numericalRulecodes("GLOBAL.300877")
          }
        }
      })
  }


  "test business global rulecodes" in {

    val stream = getClass.getResourceAsStream("/test_data/business_rulecode_test_data.csv")
    val reader = new InputStreamReader(stream)
    val csvReader = CSVReader.open(reader)

    csvReader.toStreamWithHeaders
      .foreach(unfilteredRow => {
        //Out of the csv row that is being read filter all keys that do not have a value
        val row = unfilteredRow.filter(!_._2.equals(""))
        val bnyCategoricalRuleCodes = Map(
          "BNYVL.100001" -> row.getOrElse("BNYM_ASV_STATUS_CODE", ""),
          "BNYVL.100002" -> row.getOrElse("BNYM_ASV_DESCRIPTION", ""),
          "BNYVL.100003" -> row.getOrElse("BNYM_AOV_STATUS_CODE", ""),
          "BNYVL.100004" -> row.getOrElse("BNYM_FNAME", ""),
          "BNYVL.100005" -> row.getOrElse("BNYM_LNAME", ""),
          "BNYVL.100006" -> row.getOrElse("BNYM_NAME", ""),
          "BNYVL.100007" -> row.getOrElse("BNYM_BUSNAME", ""),
          "BNYVL.100008" -> row.getOrElse("BNYM_SSN", ""),
          "BNYVL.100009" -> row.getOrElse("BNYM_DOB", ""),
          "BNYVL.100010" -> row.getOrElse("BNYM_ADDRESS", ""),
          "BNYVL.100011" -> row.getOrElse("BNYM_CITY", ""),
          "BNYVL.100012" -> row.getOrElse("BNYM_STATE", ""),
          "BNYVL.100013" -> row.getOrElse("BNYM_ZIP", ""),
          "BNYVL.100014" -> row.getOrElse("BNYM_HMPHONE", ""),
          "BNYVL.100016" -> row.getOrElse("BNYM_FNAME_MATCH", ""),
          "BNYVL.100017" -> row.getOrElse("BNYM_LNAME_MATCH", ""),
          "BNYVL.100018" -> row.getOrElse("BNYM_ADDRESS_MATCH", ""),
          "BNYVL.100020" -> row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""),
          "BNYVL.100021" -> row.getOrElse("BNYM_WKPHONE", ""),
          "BNYVL.100022" -> row.getOrElse("BNYM_PHONE", "")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredBnyCategoricalRuleCodes = bnyCategoricalRuleCodes.filter(!_._2.equals(""))

        val bnyMellonMetadata: AIVendorMetadata = new AIVendorMetadata("BNYMELLON", true, "COMPLETED", "7dac7147-c42f-44a6-a3b4-8bae8844c2a0", "22292716e8b59e8")
        val bnyOwnership: Option[AccountOwnership] = Some(AccountOwnership("Match", "Match: CITY,FNAME,LNAME,NAME,STATE,ZIP; 100"))
        val bnyStatus: Option[AccountStatus] = Some(AccountStatus(row.getOrElse("BNYM_ASV_STATUS_CODE", ""), row.getOrElse("BNYM_ASV_DESCRIPTION", ""), None, Some(""), Some(row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""))))
        val bnyResult: AIVendorResult = AIVendorResult(bnyOwnership, bnyStatus)


        val aiBnyMellonResponseMock: AIVendorResponse = new AIVendorResponse(bnyMellonMetadata, bnyResult, None, Some(new AIVendorRuleCodes(Map(), filteredBnyCategoricalRuleCodes)))

        val mbtCategoricalRuleCodes = Map(
          "MBTVL.100001" -> row.getOrElse("MBT_FNAME_MATCH", ""),
          "MBTVL.100002" -> row.getOrElse("MBT_LNAME_MATCH", ""),
          "MBTVL.100003" -> row.getOrElse("MBT_PHONE_MATCH", ""),
          "MBTVL.100004" -> row.getOrElse("MBT_DECISION_CODE", ""),
          "MBTVL.100005" -> row.getOrElse("MBT_LAST_SEEN_THR", ""),
          "MBTVL.100008" -> row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""),
          "MBTVL.100009" -> row.getOrElse("MBT_PROPERTY_MESSAGE_SIMP", ""),
          "MBTVL.FIRST_NAME_MATCH" -> row.getOrElse("MBT_FNAME_MATCH", ""),
          "MBTVL.LAST_NAME_MATCH" -> row.getOrElse("MBT_LNAME_MATCH", ""),
          "MBTVL.PHONE_MATCH" -> row.getOrElse("MBT_PHONE_MATCH", "")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredMbtCategoricalRuleCodes = mbtCategoricalRuleCodes.filter(!_._2.equals(""))


        val mbtMetadata: AIVendorMetadata = new AIVendorMetadata("MICROBILT", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val mbtStatus: Option[AccountStatus] = Some(AccountStatus("A", row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""), Some(row.getOrElse("MBT_DECISION_CODE", "")), Some(row.getOrElse("MBT_LAST_SEEN_THR", "")), None))
        val mbtResult: AIVendorResult = AIVendorResult(None, mbtStatus)

        val aiMbtResponseMock: AIVendorResponse = new AIVendorResponse(mbtMetadata, mbtResult, None, Some(new AIVendorRuleCodes(Map(), filteredMbtCategoricalRuleCodes)))
        var aiGatewayRequestJValue = aiGatewayBusinessRequest.replace("params" :: "businessEin" :: Nil, JString(row.getOrElse("ein", null)))
        aiGatewayRequestJValue = aiGatewayRequestJValue.replace("params" :: "businessPhone" :: Nil, JString(row.getOrElse("businessPhone", null)))
        aiGatewayRequestJValue = aiGatewayRequestJValue.replace("params" :: "physicalAddress" :: Nil, JString(row.getOrElse("physicalAddress", null)))
        aiGatewayRequestJValue = aiGatewayRequestJValue.replace("payment" :: "accountNumber" :: Nil, JString(row.getOrElse("accountNumber", "")))
        aiGatewayRequestJValue = aiGatewayRequestJValue.replace("payment" :: "routingNumber" :: Nil, JString(row.getOrElse("routingNumber", "")))

        val result = rulecodeService.computeVendorRulecodes("f7ec6b82-e0f8-45e2-b0cd-7b17d6721f5d", 1234,
          Seq(aiBnyMellonResponseMock, aiMbtResponseMock),
          aiGatewayRequestJValue.extract[AIGatewayRequest])

        val ruleCodeResponse = Await.result(result, Duration.Inf)

        val numericalRulecodes = ruleCodeResponse.get.numericalRulecodes
        val categoricalRulecodes = ruleCodeResponse.get.categoricalRulecodes

        assertResult(row.get("GLOBAL_SAI_BUSNAME_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300803")
        }
        assertResult(row.get("GLOBAL_SAI_PHONE_MATCH")) {
          categoricalRulecodes.get("GLOBAL.300804")
        }

        // Ignoring tests for Not Provided
        if(!row.get("GLOBAL_SAI_ADDRESS_MATCH").contains("Not Provided")){
          assertResult(row.get("GLOBAL_SAI_ADDRESS_MATCH")) {
            categoricalRulecodes.get("GLOBAL.300805")
          }
        }
//        assertResult(row("GLOBAL_SAI_ASV_SCORE").toDouble) {
//          numericalRulecodes("GLOBAL.300809")
//        }
        assertResult(row("GLOBAL_SAI_AOV_SCORE").toDouble) {
          numericalRulecodes("GLOBAL.300810")
        }
//        assertResult(row.get("GLOBAL_SAI_ASV_PATTERN")) {
//          categoricalRulecodes.get("GLOBAL.300811")
//        }
        assertResult(row.get("GLOBAL_SAI_AOV_PATTERN")) {
          categoricalRulecodes.get("GLOBAL.300812")
        }

        // busName Numerical
        if (row.contains("GLOBAL_SAI_BUSNAME_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_BUSNAME_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300828")
          }
        }
        else if (row.contains("GLOBAL_SAI_BUSNAME_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_BUSNAME_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300829")
          }
        }
        else if (row.contains("GLOBAL_SAI_BUSNAME_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_BUSNAME_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300830")
          }
        }

        // phoneMatch Numerical
        if (row.contains("GLOBAL_SAI_PHONE_MATCH_YES")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_YES").toDouble) {
            numericalRulecodes("GLOBAL.300844")
          }
        }
        else if (row.contains("GLOBAL_SAI_PHONE_MATCH_NO")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_NO").toDouble) {
            numericalRulecodes("GLOBAL.300845")
          }
        }
        else if (row.contains("GLOBAL_SAI_PHONE_MATCH_CONDITIONAL")) {
          assertResult(row("GLOBAL_SAI_PHONE_MATCH_CONDITIONAL").toDouble) {
            numericalRulecodes("GLOBAL.300846")
          }
        }

        // address Numerical
        // Ignoring tests for Not Provided
        if(!row.get("GLOBAL_SAI_ADDRESS_MATCH").contains("Not Provided")) {
          if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_YES")) {
            assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_YES").toDouble) {
              numericalRulecodes("GLOBAL.300840")
            }
          }
          else if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_NO")) {
            assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_NO").toDouble) {
              numericalRulecodes("GLOBAL.300841")
            }
          }
          else if (row.contains("GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL")) {
            assertResult(row("GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL").toDouble) {
              numericalRulecodes("GLOBAL.300842")
            }
          }
        }
      })
  }

    "test mb only global rulecodes" in {
      val stream = getClass.getResourceAsStream("/test_data/mb_rulecode_test_data.csv")
  //    val stream = getClass.getResourceAsStream("/test_data/mb_rulecode_test_data.csv")
      val reader = new InputStreamReader(stream)
      val csvReader = CSVReader.open(reader)

      csvReader.toStreamWithHeaders
        .foreach(unfilteredRow => {
          //Out of the csv row that is being read filter all keys that do not have a value
          val row = unfilteredRow.filter(!_._2.equals(""))

          val mbtCategoricalRuleCodes = Map(
            "MBTVL.100001" -> row.getOrElse("MBT_FNAME_MATCH", ""),
            "MBTVL.100002" -> row.getOrElse("MBT_LNAME_MATCH", ""),
            "MBTVL.100003" -> row.getOrElse("MBT_PHONE_MATCH", ""),
            "MBTVL.100004" -> row.getOrElse("MBT_DECISION_CODE", ""),
            "MBTVL.100005" -> row.getOrElse("MBT_LAST_SEEN_THR", ""),
            "MBTVL.100008" -> row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""),
            "MBTVL.100009" -> row.getOrElse("MBT_PROPERTY_MESSAGE_SIMP", ""),
            "MBTVL.100010" -> row.getOrElse("MBT_RETURNS_THR", ""),
            "MBTVL.100011" -> row.getOrElse("MBT_LAST_SEEN_MULTI_THR", "")
          )

          //Filter our Vendor categorical rulecodes that has a blank value
          val filteredMbtCategoricalRuleCodes = mbtCategoricalRuleCodes.filter(!_._2.equals(""))

          val mbtMetadata: AIVendorMetadata = new AIVendorMetadata("MICROBILT", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
          val mbtStatus: Option[AccountStatus] = Some(AccountStatus("A", row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""), Some(row.getOrElse("MBT_DECISION_CODE", "")), Some(row.getOrElse("MBT_LAST_SEEN_THR", "")), None))
          val mbtResult: AIVendorResult = AIVendorResult(None, mbtStatus)

          val aiMbtResponseMock: AIVendorResponse = new AIVendorResponse(mbtMetadata, mbtResult, None, Some(new AIVendorRuleCodes(Map(), filteredMbtCategoricalRuleCodes)))

          //SOCID_FNAME_MATCH_FUZZY,SOCID_LNAME_MATCH_FUZZY,SOCID_SSN,SOCID_DOB,SOCID_MATCH_SCORE,GLOBAL_SAI_SOCID_SSN_MATCH,GLOBAL_SAI_SOCID_DOB_MATCH,GLOBAL_SSN_DERIVED_FROM_SOCUREID,GLOBAL_DOB_DERIVED_FROM_SOCUREID
          val socIdCategoricalRuleCodes = Map(
            "SOCVL.100001" -> row.getOrElse("SOCID_FNAME_MATCH_FUZZY", ""),
            "SOCVL.100002" -> row.getOrElse("SOCID_LNAME_MATCH_FUZZY", ""),
            "SOCVL.100003" -> row.getOrElse("SOCID_SSN", ""),
            "SOCVL.100004" -> row.getOrElse("SOCID_DOB", ""),
            "SOCVL.100006" -> row.getOrElse("SOCID_DECEASED", "")
          )

          //Filter our Vendor categorical rulecodes that has a blank value
          val filteredSocIdCategoricalRuleCodes = socIdCategoricalRuleCodes.filter(!_._2.equals(""))

          val socIdNumericalRuleCodes: Map[String, Double] = Map(
            "SOCVL.100005" -> row.getOrElse("SOCID_MATCH_SCORE", "0.0").toDouble
          )

          val socIdMetadata: AIVendorMetadata = new AIVendorMetadata("SOCID", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
          val socIdStatus: Option[AccountStatus] = Some(AccountStatus("A", "", Some(""), Some(""), None))
          val socIdResult: AIVendorResult = AIVendorResult(None, socIdStatus)

          val socIdResponseMock: AIVendorResponse = new AIVendorResponse(socIdMetadata, socIdResult, None, Some(new AIVendorRuleCodes(socIdNumericalRuleCodes, filteredSocIdCategoricalRuleCodes)))

          var aiGatewayRequestJValue = aiGatewayRequest.replace("payment" :: "accountNumber" :: Nil, JString(row.getOrElse("accountNumber", "*********")))
          aiGatewayRequestJValue = aiGatewayRequestJValue.replace("payment" :: "routingNumber" :: Nil, JString(row.getOrElse("routingNumber", "1234556")))

          val result = rulecodeService.computeVendorRulecodes("f7ec6b82-e0f8-45e2-b0cd-7b17d6721f5d", 1234,
            Seq(aiMbtResponseMock, socIdResponseMock),
            aiGatewayRequestJValue.extract[AIGatewayRequest])

          val ruleCodeResponse = Await.result(result, Duration.Inf)

          val numericalRulecodes = ruleCodeResponse.get.numericalRulecodes
          val categoricalRulecodes = ruleCodeResponse.get.categoricalRulecodes

          assertResult(row("GLOBAL_SAI_ASV_SCORE").toDouble) {
            numericalRulecodes("GLOBAL.300809")
          }
          assertResult(row.get("GLOBAL_SAI_ASV_PATTERN")) {
            categoricalRulecodes.get("GLOBAL.300811")
          }

          if (row.contains("GLOBAL_SAI_ROUTING_NUMBER_INVALID")) {
            assertResult(row("GLOBAL_SAI_ROUTING_NUMBER_INVALID").toDouble) {
              numericalRulecodes("GLOBAL.300870")
            }
          }

          if (row.contains("GLOBAL_ACCOUNT_WARNING")) {
            assertResult(row("GLOBAL_ACCOUNT_WARNING").toDouble) {
              numericalRulecodes("GLOBAL.300872")
            }
          }

          if (row.contains("GLOBAL_ACCOUNT_NO_INFO")) {
            assertResult(row("GLOBAL_ACCOUNT_NO_INFO").toDouble) {
              numericalRulecodes("GLOBAL.300873")
            }
          }

          if (row.contains("GLOBAL_ACC_STRUCTURE_ISSUE")) {
            assertResult(row("GLOBAL_ACC_STRUCTURE_ISSUE").toDouble) {
              numericalRulecodes("GLOBAL.300876")
            }
          }

          // Last seen threshold Numerical
          if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_15")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_15").toDouble) {
              numericalRulecodes("GLOBAL.300878")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_30")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_30").toDouble) {
              numericalRulecodes("GLOBAL.300879")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_45")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_45").toDouble) {
              numericalRulecodes("GLOBAL.300880")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_60")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_60").toDouble) {
              numericalRulecodes("GLOBAL.300881")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_75")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_75").toDouble) {
              numericalRulecodes("GLOBAL.300882")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_90")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_90").toDouble) {
              numericalRulecodes("GLOBAL.300883")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_120")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_120").toDouble) {
              numericalRulecodes("GLOBAL.300884")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_150")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_150").toDouble) {
              numericalRulecodes("GLOBAL.300885")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_180")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_180").toDouble) {
              numericalRulecodes("GLOBAL.300886")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_210")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_210").toDouble) {
              numericalRulecodes("GLOBAL.300887")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_240")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_240").toDouble) {
              numericalRulecodes("GLOBAL.300888")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_270")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_270").toDouble) {
              numericalRulecodes("GLOBAL.300889")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_300")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_300").toDouble) {
              numericalRulecodes("GLOBAL.300892")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_365")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_365").toDouble) {
              numericalRulecodes("GLOBAL.300893")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_730")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_730").toDouble) {
              numericalRulecodes("GLOBAL.300894")
            }
          }
          else if (row.contains("GLOBAL_LAST_SEEN_THRESHOLD_Y")) {
            assertResult(row("GLOBAL_LAST_SEEN_THRESHOLD_Y").toDouble) {
              numericalRulecodes("GLOBAL.300895")
            }
          }

          //Numerical return history indicator
          if (row.contains("GLOBAL_RETURN_HISTORY_INDICATOR")) {
              assertResult(row("GLOBAL_RETURN_HISTORY_INDICATOR").toDouble) {
              numericalRulecodes("GLOBAL.300891")
            }
          }
        })
    }

  "test vrval global rulecodes" in {
    val stream = getClass.getResourceAsStream("/test_data/vrval_global.csv")
    val reader = new InputStreamReader(stream)
    val csvReader = CSVReader.open(reader)

    csvReader.toStreamWithHeaders
      .foreach(unfilteredRow => {
        val row = unfilteredRow.filter(!_._2.equals(""))

        val bnyCategoricalRuleCodes = Map(
          "BNYVL.100016" -> "Match",
          "BNYVL.100017" -> "Match"
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredBnyCategoricalRuleCodes = bnyCategoricalRuleCodes.filter(!_._2.equals(""))

        val bnyMellonMetadata: AIVendorMetadata = new AIVendorMetadata("BNYMELLON", true, "COMPLETED", "7dac7147-c42f-44a6-a3b4-8bae8844c2a0", "22292716e8b59e8")
        val bnyOwnership: Option[AccountOwnership] = Some(AccountOwnership("Match", "Match: CITY,FNAME,LNAME,NAME,STATE,ZIP; 100"))
        val bnyStatus: Option[AccountStatus] = Some(AccountStatus(row.getOrElse("BNYM_ASV_STATUS_CODE", ""), row.getOrElse("BNYM_ASV_DESCRIPTION", ""), None, Some(""), Some(row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""))))
        val bnyResult: AIVendorResult = AIVendorResult(bnyOwnership, bnyStatus)


        val aiBnyMellonResponseMock: AIVendorResponse = new AIVendorResponse(bnyMellonMetadata, bnyResult, None, Some(new AIVendorRuleCodes(Map(), filteredBnyCategoricalRuleCodes)))


        val mbtCategoricalRuleCodes = Map(
          "MBTVL.100001" -> row.getOrElse("MBT_FNAME_MATCH", ""),
          "MBTVL.100002" -> row.getOrElse("MBT_LNAME_MATCH", ""),
          "MBTVL.100003" -> row.getOrElse("MBT_PHONE_MATCH", ""),
          "MBTVL.100004" -> row.getOrElse("MBT_DECISION_CODE", ""),
          "MBTVL.100005" -> row.getOrElse("MBT_LAST_SEEN_THR", ""),
          "MBTVL.100008" -> row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""),
          "MBTVL.100009" -> row.getOrElse("MBT_PROPERTY_MESSAGE_SIMP", ""),
          "MBTVL.100010" -> row.getOrElse("MBT_RETURNS_THR", "1.0"),
          "MBTVL.100011" -> row.getOrElse("MBT_LAST_SEEN_MULTI_THR", "1.0")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredMbtCategoricalRuleCodes = mbtCategoricalRuleCodes.filter(!_._2.equals(""))

        val mbtMetadata: AIVendorMetadata = new AIVendorMetadata("MICROBILT", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val mbtStatus: Option[AccountStatus] = Some(AccountStatus("A", row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""), Some(row.getOrElse("MBT_DECISION_CODE", "")), Some(row.getOrElse("MBT_LAST_SEEN_THR", "")), None))
        val mbtResult: AIVendorResult = AIVendorResult(None, mbtStatus)

        val aiMbtResponseMock: AIVendorResponse = new AIVendorResponse(mbtMetadata, mbtResult, None, Some(new AIVendorRuleCodes(Map(), filteredMbtCategoricalRuleCodes)))

        val socIdCategoricalRuleCodes = Map(
          "SOCVL.100001" -> row.getOrElse("SOCID_FNAME_MATCH_FUZZY", ""),
          "SOCVL.100002" -> row.getOrElse("SOCID_LNAME_MATCH_FUZZY", ""),
          "SOCVL.100006" -> row.getOrElse("SOCID_DECEASED", "")
        )

        val filteredSocIdCategoricalRuleCodes = socIdCategoricalRuleCodes.filter(!_._2.equals(""))

        val socIdNumericalRuleCodes: Map[String, Double] = Map(
          "SOCVL.100005" -> row.getOrElse("SOCID_MATCH_SCORE", "0.0").toDouble
        )

        val socIdMetadata: AIVendorMetadata = new AIVendorMetadata("SOCID", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val socIdStatus: Option[AccountStatus] = Some(AccountStatus("A", "", Some(""), Some(""), None))
        val socIdResult: AIVendorResult = AIVendorResult(None, socIdStatus)

        val socIdResponseMock: AIVendorResponse = new AIVendorResponse(socIdMetadata, socIdResult, None, Some(new AIVendorRuleCodes(socIdNumericalRuleCodes, filteredSocIdCategoricalRuleCodes)))

        val vrvalCategoricalRuleCodes = Map(
          "VRVAL.100036" -> "1.0"
        )

        val filteredVRVALCategoricalRuleCodes = vrvalCategoricalRuleCodes.filter(!_._2.equals(""))

        val vrvalNumericalRuleCodes: Map[String, Double] = Map(
          "VRVAL.100003" -> row.getOrElse("VR_SSN_DECEASED", "0.0").toDouble,
          "VRVAL.100009" -> row.getOrElse("VR_IDENTITY_ISDECEASED", "0.0").toDouble
        )

        val vrvalIdMetadata: AIVendorMetadata = new AIVendorMetadata("VRVAL", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val vrvalResult: AIVendorResult = AIVendorResult(None, None)

        val vrvalResponseMock: AIVendorResponse = new AIVendorResponse(vrvalIdMetadata, vrvalResult, None, Some(new AIVendorRuleCodes(vrvalNumericalRuleCodes, filteredVRVALCategoricalRuleCodes)))

        val result = rulecodeService.computeVendorRulecodes("f7ec6b82-e0f8-45e2-b0cd-7b17d6721f5d", 1234,
          Seq(aiMbtResponseMock, socIdResponseMock, vrvalResponseMock, aiBnyMellonResponseMock),
          aiGatewayRequest.extract[AIGatewayRequest])

        val ruleCodeResponse = Await.result(result, Duration.Inf)

        val numericalRulecodes = ruleCodeResponse.get.numericalRulecodes

        if (row.contains("GLOBAL_DECEASED_INDICATOR")) {
          assertResult(row("GLOBAL_DECEASED_INDICATOR").toDouble) {
            numericalRulecodes("GLOBAL.300877")
          }
        }
      })
  }

  "test global deceased rulecodes" in {
    val stream = getClass.getResourceAsStream("/test_data/global_deceased_check.csv")
    val reader = new InputStreamReader(stream)
    val csvReader = CSVReader.open(reader)

    csvReader.toStreamWithHeaders
      .foreach(unfilteredRow => {
        val row = unfilteredRow.filter(!_._2.equals(""))

        val bnyCategoricalRuleCodes = Map(
          "BNYVL.100016" -> "Match",
          "BNYVL.100017" -> "Match"
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredBnyCategoricalRuleCodes = bnyCategoricalRuleCodes.filter(!_._2.equals(""))

        val bnyMellonMetadata: AIVendorMetadata = new AIVendorMetadata("BNYMELLON", true, "COMPLETED", "7dac7147-c42f-44a6-a3b4-8bae8844c2a0", "22292716e8b59e8")
        val bnyOwnership: Option[AccountOwnership] = Some(AccountOwnership("Match", "Match: CITY,FNAME,LNAME,NAME,STATE,ZIP; 100"))
        val bnyStatus: Option[AccountStatus] = Some(AccountStatus(row.getOrElse("BNYM_ASV_STATUS_CODE", ""), row.getOrElse("BNYM_ASV_DESCRIPTION", ""), None, Some(""), Some(row.getOrElse("BNYM_EWS_DIRECT_CONTRIBUTOR", ""))))
        val bnyResult: AIVendorResult = AIVendorResult(bnyOwnership, bnyStatus)


        val aiBnyMellonResponseMock: AIVendorResponse = new AIVendorResponse(bnyMellonMetadata, bnyResult, None, Some(new AIVendorRuleCodes(Map(), filteredBnyCategoricalRuleCodes)))


        val mbtCategoricalRuleCodes = Map(
          "MBTVL.100001" -> row.getOrElse("MBT_FNAME_MATCH", ""),
          "MBTVL.100002" -> row.getOrElse("MBT_LNAME_MATCH", ""),
          "MBTVL.100003" -> row.getOrElse("MBT_PHONE_MATCH", ""),
          "MBTVL.100004" -> row.getOrElse("MBT_DECISION_CODE", "1.0"),
          "MBTVL.100005" -> row.getOrElse("MBT_LAST_SEEN_THR", ""),
          "MBTVL.100008" -> row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""),
          "MBTVL.100009" -> row.getOrElse("MBT_PROPERTY_MESSAGE_SIMP", ""),
          "MBTVL.100010" -> row.getOrElse("MBT_RETURNS_THR", "1.0"),
          "MBTVL.100011" -> row.getOrElse("MBT_LAST_SEEN_MULTI_THR", "1.0")
        )

        //Filter our Vendor categorical rulecodes that has a blank value
        val filteredMbtCategoricalRuleCodes = mbtCategoricalRuleCodes.filter(!_._2.equals(""))

        val mbtMetadata: AIVendorMetadata = new AIVendorMetadata("MICROBILT", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val mbtStatus: Option[AccountStatus] = Some(AccountStatus("A", row.getOrElse("MBT_PROPERTY_MESSAGE_DIR", ""), Some(row.getOrElse("MBT_DECISION_CODE", "")), Some(row.getOrElse("MBT_LAST_SEEN_THR", "")), None))
        val mbtResult: AIVendorResult = AIVendorResult(None, mbtStatus)

        val aiMbtResponseMock: AIVendorResponse = new AIVendorResponse(mbtMetadata, mbtResult, None, Some(new AIVendorRuleCodes(Map(), filteredMbtCategoricalRuleCodes)))

        val socIdCategoricalRuleCodes = Map(
          "SOCVL.100001" -> row.getOrElse("SOCID_FNAME_MATCH_FUZZY", ""),
          "SOCVL.100002" -> row.getOrElse("SOCID_LNAME_MATCH_FUZZY", ""),
          "SOCVL.100006" -> row.getOrElse("SOCID_DECEASED", "")
        )

        val filteredSocIdCategoricalRuleCodes = socIdCategoricalRuleCodes.filter(!_._2.equals(""))

        val socIdNumericalRuleCodes: Map[String, Double] = Map(
          "SOCVL.100005" -> row.getOrElse("SOCID_MATCH_SCORE", "0.0").toDouble
        )

        val socIdMetadata: AIVendorMetadata = new AIVendorMetadata("SOCID", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val socIdStatus: Option[AccountStatus] = Some(AccountStatus("A", "", Some(""), Some(""), None))
        val socIdResult: AIVendorResult = AIVendorResult(None, socIdStatus)

        val socIdResponseMock: AIVendorResponse = new AIVendorResponse(socIdMetadata, socIdResult, None, Some(new AIVendorRuleCodes(socIdNumericalRuleCodes, filteredSocIdCategoricalRuleCodes)))

        val vrvalCategoricalRuleCodes = Map(
          "VRVAL.100036" -> "1.0"
        )

        val filteredVRVALCategoricalRuleCodes = vrvalCategoricalRuleCodes.filter(!_._2.equals(""))

        val vrvalNumericalRuleCodes: Map[String, Double] = Map(
          "VRVAL.100062" -> row.getOrElse("VRVAL.100062", "0.0").toDouble,
          "VRVAL.100115" -> row.getOrElse("VRVAL.100115", "0.0").toDouble,
          "VRVAL.100009" -> row.getOrElse("VRVAL.100009", "0.0").toDouble,
          "VRVAL.100003" -> row.getOrElse("VRVAL.100003", "0.0").toDouble,
          "VRVAL.100201" -> row.getOrElse("VRVAL.100201", "0.0").toDouble,
          "VRVAL.100203" -> row.getOrElse("VRVAL.100203", "0.0").toDouble,
          "VRVAL.100204" -> row.getOrElse("VRVAL.100204", "0.0").toDouble,
          "VRVAL.100041" -> row.getOrElse("VRVAL.100041", "0.0").toDouble
        )

        val vrvalIdMetadata: AIVendorMetadata = new AIVendorMetadata("VRVAL", true, "COMPLETED", "E5E0F1B6-23F6-43AB-AB5E-EC9FA13F4FA9", "22292716e8b59e9")
        val vrvalResult: AIVendorResult = AIVendorResult(None, None)

        val vrvalResponseMock: AIVendorResponse = new AIVendorResponse(vrvalIdMetadata, vrvalResult, None, Some(new AIVendorRuleCodes(vrvalNumericalRuleCodes, filteredVRVALCategoricalRuleCodes)))

        val result = rulecodeService.computeVendorRulecodes("f7ec6b82-e0f8-45e2-b0cd-7b17d6721f5d", 1234,
          Seq(aiMbtResponseMock, socIdResponseMock, vrvalResponseMock, aiBnyMellonResponseMock),
          aiGatewayRequest.extract[AIGatewayRequest])

        val ruleCodeResponse = Await.result(result, Duration.Inf)

        val numericalRulecodes = ruleCodeResponse.get.numericalRulecodes

        if (row.contains("GLOBAL.300898")) {
          assertResult(row("GLOBAL.300898").toDouble) {
            numericalRulecodes("GLOBAL.300898")
          }
        }

        if (row.contains("GLOBAL.300897")) {
          assertResult(row("GLOBAL.300897").toDouble) {
            numericalRulecodes("GLOBAL.300897")
          }
        }
      })
  }

  "RuleCodeService" - {
    "should correctly generate rule codes for account status indicators" - {
      "GLOBAL.300905 (Primary Failure Indicator)" - {
        "when TRICE indicates failed status" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100005" -> "failed"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes("GLOBAL.300905") shouldBe 1.0
        }

        "when TRICE indicates success status" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100005" -> "success"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes.get("GLOBAL.300905") shouldBe None
        }
      }

      "GLOBAL.300906 (Account Closed Indicator)" - {
        "when TRICE indicates bank account closed" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "Bank account closed"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes("GLOBAL.300906") shouldBe 1.0
        }

        "when BNYVL indicates account terminated" in {
          val bnyvlCategoricalRuleCodes = Map(
            "BNYVL_100002" -> "Account Terminated"
          )
          val bnyvlResponse = createVendorResponse("BNYVL", bnyvlCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(bnyvlResponse))
          result.numericalRulecodes("GLOBAL.300906") shouldBe 1.0
        }

        "when CONVL indicates closed account" in {
          val convlCategoricalRuleCodes = Map(
            "CONVL_100002" -> "Closed"
          )
          val convlResponse = createVendorResponse("CONVL", convlCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(convlResponse))
          result.numericalRulecodes("GLOBAL.300906") shouldBe 1.0
        }
        "when account is not closed" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "active"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes.get("GLOBAL.300906") shouldBe None
        }
      }

      "GLOBAL.300907 (Account Blocked Indicator)" - {
        "when TRICE indicates bank account blocked" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "Bank account blocked"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes("GLOBAL.300907") shouldBe 1.0
        }

        "when account is not blocked" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "active"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes.get("GLOBAL.300907") shouldBe None
        }
      }

      "GLOBAL.300908 (Invalid Account Number Indicator)" - {
        "when TRICE indicates invalid account number" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "Creditor account number invalid or missing"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes("GLOBAL.300908") shouldBe 1.0
        }

        "when MBTVL indicates invalid account number" in {
          val mbtvlCategoricalRuleCodes = Map(
            "MBTVL_100008" -> "Account number is invalid"
          )
          val mbtvlResponse = createVendorResponse("MICROBILT", mbtvlCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(mbtvlResponse))
          result.numericalRulecodes("GLOBAL.300908") shouldBe 1.0
        }

        "when account number is valid" in {
          val triceCategoricalRuleCodes = Map(
            "TRICE_100003" -> "active"
          )
          val triceResponse = createVendorResponse("TRICE", triceCategoricalRuleCodes)
          
          val result = computeRuleCodes(Seq(triceResponse))
          result.numericalRulecodes.get("GLOBAL.300908") shouldBe None
        }
      }
    }
  }

  private def createVendorResponse(vendor: String, categoricalRuleCodes: Map[String, String]): AIVendorResponse = {
    val metadata = AIVendorMetadata(vendor, true, "COMPLETED", "test-id", "test-version")
    val result = AIVendorResult(None, None)
    
    val updatedCategoricalRuleCodes = categoricalRuleCodes ++ Map(
      "MBTVL.100004" -> "A",
      "MBTVL.100010" -> "10",
      "MBTVL.100011" -> "15",
      "TRICE.100005" -> "SUCCESS"
    )
    
    val ruleCodes = AIVendorRuleCodes(Map(), updatedCategoricalRuleCodes)
    AIVendorResponse(metadata, result, None, Some(ruleCodes))
  }

  private def computeRuleCodes(vendorResponses: Seq[AIVendorResponse]): AIGatewayGlobalRulecodes = {
    val dummyRequest = AIGatewayRequest(
      metadata = AIGatewayMetadata(
        accountId = 1L,
        environmentTypeId = 1,
        maskPii = false,
        transactionId = "test-tx-id",
        memo = None,
        ultimateSendingPartyId = None,
        submissionDate = None
      ),
      payment = AIGatewayPayment(
        accountNumber = "*********0",
        routingNumber = "*********",
        accountCountry = "US",
        inquiries = Set("AVAILABILITY")
      ),
      params = AIGatewayParams(
        firstName = Some("John"),
        surName = Some("Doe"),
        businessName = None,
        physicalAddress = None,
        physicalAddress2 = None,
        city = None,
        state = None,
        zip = None,
        country = Some("US"),
        nationalId = None,
        mobileNumber = None,
        dob = None,
        businessEin = None
      ),
      vendors = Seq(AIGatewayVendorConfig("CONVL", 1200), AIGatewayVendorConfig("MBTVL", 1200), AIGatewayVendorConfig("TRICE", 1200))
    )
    Await.result(rulecodeService.computeVendorRulecodes(
      "test-id",
      1234,
      vendorResponses,
      dummyRequest
    ), Duration.Inf).get
  }
}

