package me.socure.ai.gateway.rulecode

case class RuleCodeTestData(
                             id: String,
                             BNYM_ASV_STATUS_CODE: Option[String] = None,
                             BNYM_ASV_DESCRIPTION: Option[String] = None,
                             BNYM_AOV_STATUS_CODE: Option[String] = None,
                             BNYM_FNAME: Option[String] = None,
                             BNYM_LNAME: Option[String] = None,
                             BNYM_NAME: Option[String] = None,
                             BNYM_BUSNAME: Option[String] = None,
                             BNYM_SSN: Option[String] = None,
                             BNYM_DOB: Option[String] = None,
                             BNYM_ADDRESS: Option[String] = None,
                             BNYM_CITY: Option[String] = None,
                             BNYM_STATE: Option[String] = None,
                             BNYM_ZIP: Option[String] = None,
                             BNYM_HMPHONE: Option[String] = None,
                             BNYM_WKPHONE: Option[String] = None,
                             BNYM_PHONE: Option[String] = None,
                             BNY<PERSON>_MATCH_SCORE: Option[String] = None,
                             BNYM_FNAME_MATCH: Option[String] = None,
                             BNYM_LNAME_MATCH: Option[String] = None,
                             BNYM_ADDRESS_MATCH: Option[String] = None,
                             BNYM_EWS_DIRECT_CONTRIBUTOR: Option[String] = None,
                             MBT_FNAME_MATCH: Option[String] = None,
                             MBT_LNAME_MATCH: Option[String] = None,
                             MBT_PHONE_MATCH: Option[String] = None,
                             MBT_DECISION_CODE: Option[String] = None,
                             MBT_LAST_SEEN_THR: Option[String] = None,
                             MBT_LAST_SEEN_DAYS: Option[String] = None,
                             MBT_PROPERTY_MESSAGE_DIR: Option[String] = None,
                             MBT_PROPERTY_MESSAGE_SIMP: Option[String] = None,
                             GLOBAL_SAI_FNAME_MATCH: Option[String] = None,
                             GLOBAL_SAI_LNAME_MATCH: Option[String] = None,
                             GLOBAL_SAI_PHONE_MATCH: Option[String] = None,
                             GLOBAL_SAI_ADDRESS_MATCH: Option[String] = None,
                             GLOBAL_SAI_SSN_MATCH: Option[String] = None,
                             GLOBAL_SAI_DOB_MATCH: Option[String] = None,
                             GLOBAL_SAI_ASV_SCORE: Option[String] = None,
                             GLOBAL_SAI_AOV_SCORE: Option[String] = None,
                             GLOBAL_SAI_ASV_PATTERN: Option[String] = None,
                             GLOBAL_SAI_AOV_PATTERN: Option[String] = None,
                             GLOBAL_SAI_FNAME_MATCH_YES: Option[String] = None,
                             GLOBAL_SAI_FNAME_MATCH_NO: Option[String] = None,
                             GLOBAL_SAI_FNAME_MATCH_CONDITIONAL: Option[String] = None,
                             GLOBAL_SAI_LNAME_MATCH_YES: Option[String] = None,
                             GLOBAL_SAI_LNAME_MATCH_NO: Option[String] = None,
                             GLOBAL_SAI_LNAME_MATCH_CONDITIONAL: Option[String] = None,
                             GLOBAL_SAI_ADDRESS_MATCH_YES: Option[String] = None,
                             GLOBAL_SAI_ADDRESS_MATCH_NO: Option[String] = None,
                             GLOBAL_SAI_ADDRESS_MATCH_CONDITIONAL: Option[String] = None,
                             GLOBAL_SAI_PHONE_MATCH_YES: Option[String] = None,
                             GLOBAL_SAI_PHONE_MATCH_NO: Option[String] = None,
                             GLOBAL_SAI_PHONE_MATCH_CONDITIONAL: Option[String] = None,
                             GLOBAL_SAI_ROUTING_NUMBER_INVALID: Option[String] = None,
                             GLOBAL_ACCOUNT_WARNING: Option[String] = None,
                             GLOBAL_ACCOUNT_NO_INFO: Option[String] = None,
                             I301: Option[String] = None,
                             I302: Option[String] = None,
                             I304: Option[String] = None,
                             I305: Option[String] = None,
                             I316: Option[String] = None,
                             I317: Option[String] = None,
                             I319: Option[String] = None,
                             I320: Option[String] = None,
                             R301: Option[String] = None,
                             R302: Option[String] = None,
                             R306: Option[String] = None,
                             R307: Option[String] = None,
                             R309: Option[String] = None,
                             R311: Option[String] = None,
                             R312: Option[String] = None,
                             R313: Option[String] = None
                           )
