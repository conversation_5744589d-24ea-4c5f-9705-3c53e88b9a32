<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-gateway</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ai-gateway-service</artifactId>
    <version>${revision}</version>
    <properties>
        <socure.commons.version>${revision}</socure.commons.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-gateway-rest</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-gateway-rulecode</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-gateway-grpc</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>ai-trice-service</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-openapi3-scalatra</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ms-dependencies</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>${io.netty.version}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>${io.netty.version}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec</artifactId>
            <version>${io.netty.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>2.3</version>
                <executions>
                    <!-- Run shade goal on package phase -->
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>me.socure.ai.gateway.service.Main</mainClass>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>application.json</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
