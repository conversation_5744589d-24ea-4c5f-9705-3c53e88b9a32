package me.socure.ai.gateway.service

import com.google.common.util.concurrent.ServiceManager
import com.google.inject.Guice
import com.typesafe.config.Config
import me.socure.ai.gateway.common.util.module.{ResponseParserModule, ScorerModule}
import me.socure.ai.gateway.grpc.service.GatewayServiceGrpc.GatewayService
import me.socure.ai.gateway.grpc.service.module.GatewayServiceModule
import me.socure.ai.gateway.reasoncode.module.AIGatewayReasonCodeModule
import me.socure.ai.gateway.rest.module.{AIGatewayRestServiceModule, HMACHttpVerifierModule}
import me.socure.ai.gateway.service.module.{OpenAPIModule, ServiceManagerModule}
import me.socure.ai.gateway.vendor.service.module.AIGatewayVendorServiceModule
import me.socure.ai.trice.module.TriceModule
import me.socure.common.clock.RealClockModule
import me.socure.common.config._
import me.socure.common.environment.AppRegionModule
import me.socure.common.guice.module.CommonModule
import me.socure.common.jettythreadpool.factory.JettyThreadPoolModule
import me.socure.common.microservice.defaults.DefaultMicroservice
import net.codingwell.scalaguice.InjectorExtensions._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success, Try}

object Main extends DefaultMicroservice {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override def startUp(args: Array[String]): Unit = {
    logger.info("startup Method()")
    Try {
      val injector = Guice.createInjector(
        new CommonModule,
        new AppRegionModule,
        new RealClockModule,
        new JettyThreadPoolModule,
        new IntConfigurationModule("jmx.port"),
        new OpenAPIModule,
        new ServiceManagerModule,
        new HMACHttpVerifierModule,
        new AIGatewayRestServiceModule,
        new AIGatewayReasonCodeModule,
        new AIGatewayVendorServiceModule,
        new GatewayServiceModule,
        new InputOnlyRulecodeConfigManagerModule,
        new InputOnlyRuleCodeGenerationServiceModule,
        new RuleCodeInputResolverModule,
        new ContextCacheModule,
        new ResponseParserModule,
        new ScorerModule,
        new TriceModule
      )

      val theServices = injector.instance[ServiceManager]
      val gatewayService = injector.instance[GatewayService]
      val executionContext = injector.instance[ExecutionContext]
      val config = injector.instance[Config]

      logger.info("Starting gRPC server")
      val grpcServerPort = Try(config.getInt("grpc.server.port")).getOrElse(6000)
      //      val serverBuilder = ServerBuilder.forPort(grpcServerPort)
      //      serverBuilder.addService(GatewayServiceGrpc.bindService(gatewayService, executionContext))
      //      serverBuilder.build().start()

      theServices.startAsync()

    } match {
      case Success(_) =>
      case Failure(ex) =>
        logger.error("Unable to start service", ex)
        System.exit(1)
    }
  }

}
