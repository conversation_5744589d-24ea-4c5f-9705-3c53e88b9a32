package me.socure.ai.gateway.service.module

import com.google.inject.{AbstractModule, Provides, Singleton}
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.{Contact, Info}

class OpenAPIModule extends AbstractModule {

  @Provides
  @Singleton
  def get(): OpenAPI = {
    new OpenAPI()
      .info(new Info()
        .description("AI Gateway Service")
        .contact(new Contact()
          .email("<EMAIL>")
          .name("Socure Inc.")
          .url("https://info.socure.com/get-started/")
        )
        .termsOfService("https://www.socure.com/terms")
      )
  }

}
