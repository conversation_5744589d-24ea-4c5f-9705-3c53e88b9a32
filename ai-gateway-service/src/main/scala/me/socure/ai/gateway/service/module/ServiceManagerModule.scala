package me.socure.ai.gateway.service.module

import com.google.common.util.concurrent.ServiceManager.Listener
import com.google.common.util.concurrent.{AbstractIdleService, MoreExecutors, Service, ServiceManager}
import com.google.inject.{AbstractModule, Provides, Singleton}

class ServiceManagerModule extends AbstractModule {

  @Provides
  @Singleton
  def get(services: java.util.Set[AbstractIdleService]): ServiceManager = {
    val manager = new ServiceManager(services)

    manager.addListener(new Listener() {
      override def stopped(): Unit = {}

      override def healthy(): Unit = {
        // Services have been initialized and are healthy, start accepting requests...
      }

      override def failure(service: Service): Unit = {
        //TODO:  log, notify a load balancer, or some other action.  For now we will just exit. (Terminate jvm)
        System.exit(1)
      }

    }, MoreExecutors.directExecutor())
    manager
  }

}