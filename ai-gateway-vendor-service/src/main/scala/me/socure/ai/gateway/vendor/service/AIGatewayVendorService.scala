package me.socure.ai.gateway.vendor.service

import me.socure.ai.gateway.common.models.{AIGatewayRequest, AIGatewayResponse}
import me.socure.ai.gateway.grpc.resource.GatewayResponse
import me.socure.model.ErrorResponse

import scala.concurrent.Future

trait AIGatewayVendorService {

  def process(request: AIGatewayRequest): Future[Either[ErrorResponse, AIGatewayResponse]]

  def processGrpc(request: AIGatewayRequest): Future[Either[ErrorResponse, GatewayResponse]]

}