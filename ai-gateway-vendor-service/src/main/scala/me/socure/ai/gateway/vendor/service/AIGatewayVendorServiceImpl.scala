package me.socure.ai.gateway.vendor.service

import com.google.inject.Inject
import me.socure.ai.gateway.common.models.vendor.{AIVendorMetadata, AIVendorRequest, AIVendorResponse, AIVendorResponseStatus, AIVendorResponseWithStatus, AIVendorResult, AIVendorRuleCodes, AccountOwnership}
import me.socure.ai.gateway.common.models.{AIGatewayRequest, AIGatewayResponse, AIGatewayScore, AIGatewayVendorConfig, AIGatewayVendors}
import me.socure.ai.gateway.common.util._
import me.socure.ai.gateway.grpc.resource.{GatewayResponse, GatewayScore, GlobalRulecodes}
import me.socure.ai.gateway.reasoncode.ReasonCodeResolver
import me.socure.ai.gateway.rulecode.RuleCodeService
import me.socure.ai.gateway.vendor.service.client.AIVendorServiceGenericClient
import me.socure.common.logger.{Transaction<PERSON><PERSON><PERSON>og<PERSON>, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.model.ErrorResponse

import java.util.concurrent.{ConcurrentHashMap, ConcurrentMap, ScheduledExecutorService, TimeUnit}
import java.util.function.BiFunction
import scala.concurrent.{ExecutionContext, Future, Promise}
import scala.util.control.NonFatal
import collection.JavaConverters._

class AIGatewayVendorServiceImpl @Inject()(
                                            vendorClients: Map[String, AIVendorServiceGenericClient[AIVendorRequest, AIVendorResponse]],
                                            ruleCodeService: RuleCodeService,
                                            reasonCodeResolver: ReasonCodeResolver,
                                            responseParser: ResponseParser,
                                            scorer: Scorer,
                                            scheduler: ScheduledExecutorService,
                                            dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate
                                          )(implicit ec: ExecutionContext) extends AIGatewayVendorService {
  import AIGatewayVendorServiceImpl._

  override def process(request: AIGatewayRequest): Future[Either[ErrorResponse, AIGatewayResponse]] = {
    // Supports up to 16 vendors; constructor should be used with initial capacity if we have more vendors
    val state: ConcurrentMap[String, AIVendorResponseWithStatus] = new ConcurrentHashMap[String, AIVendorResponseWithStatus]()
    val resultPromise = Promise[Either[ErrorResponse, AIGatewayResponse]]()
    implicit val trxId: TrxId = TrxId(request.metadata.transactionId)

    dynamicControlCenterV2Evaluate.evaluate(FlagGroupName, FlagName, Option.empty, Option.apply(Tuple2.apply("accountId", request.metadata.accountId.toString)), Array.empty).flatMap {
      //If flag is active we use the fallback logic
      case Right(response) if response.isFlagActive =>
        txnLogger.info(s"Success while fetching flag ${FlagName} from group ${FlagGroupName} for accountId ${request.metadata.accountId}; flag is enabled")
        processResponseEarly(request)(state, resultPromise)
        processResponseNormally(request)(state, resultPromise)
        resultPromise.future
      //Fallback logic disabled via flag
      case Right(response) if !response.isFlagActive =>
        txnLogger.info(s"Success while fetching flag ${FlagName} from group ${FlagGroupName} for accountId ${request.metadata.accountId}; flag is disabled")
        processResponseNormally(request)(state, resultPromise)
        resultPromise.future
      //Error evaluating flag
      case Left(error) =>
        txnLogger.error(s"Error while fetching flag ${FlagName} from group ${FlagGroupName} for accountId ${request.metadata.accountId}; defaulting to false", error)
        processResponseNormally(request)(state, resultPromise)
        resultPromise.future
    }
  }

  private def processResponseEarly(request: AIGatewayRequest)(state: ConcurrentMap[String, AIVendorResponseWithStatus], resultPromise: Promise[Either[ErrorResponse, AIGatewayResponse]])(implicit trxId: TrxId) = {
    scheduler.schedule(new Runnable() {
      override def run(): Unit = {
        Future {
          txnLogger.debug("Checking for Trice v/s Microbilt Responses for early results")
          // return early only if Trice is still in processing state but Microbilt responses are complete
          if (state.containsKey("TRICE") && state.get("TRICE").status.equals(AIVendorResponseStatus.PROCESSING)) {
            val mbtvlVendorResponseWithStatus = if(state.containsKey("MBTVL")) Some(state.get("MBTVL")) else None
            // Condition to check for: MBTVL_100004 == 'A' or MBTVL_100004 == 'W' or MBTVL_100004 == 'D'
            val mbtvlResponseComplete = mbtvlVendorResponseWithStatus.exists(_.status.equals(AIVendorResponseStatus.COMPLETE))
            val mbtvl100004 = mbtvlVendorResponseWithStatus.flatMap(_.data.flatMap(_.rulecodes.flatMap(_.categoricalRulecodes.get("MBTVL.100004"))))

            if (
              mbtvlResponseComplete &&
              (
                mbtvl100004.exists(_.equalsIgnoreCase("A")) ||
                mbtvl100004.exists(_.equalsIgnoreCase("W")) ||
                mbtvl100004.exists(_.equalsIgnoreCase("D"))
              )
            ) {
              //Continue remaining logic here; calling this method will try to complete the promise
              resultPromise.tryCompleteWith {
                txnLogger.debug("Trying to finish with early processing since Trice is still processing after 2.4s")
                processFinalVendorResponses(
                  request,
                  //We need to include the fallback rulecode for Trice here
                  state.values().asScala.toSeq.flatMap(_.data) :+ SkippedTriceVendorResponse(trxId)
                )(resultPromise)
              }
            }
          }
          // else do nothing; the normal processing flow will take care of fulfilling the promise
        }
      }
    },
      2400,
      TimeUnit.MILLISECONDS
    )
  }

  private def processResponseNormally(request: AIGatewayRequest)(state: ConcurrentMap[String, AIVendorResponseWithStatus], resultPromise: Promise[Either[ErrorResponse, AIGatewayResponse]])(implicit trxId: TrxId) = {
    val transformedRequest = request.toVendorRequest
    val availableVendors = request.vendors.filter(vendor => vendorClients.contains(vendor.name))
    if (availableVendors.isEmpty) {
      resultPromise.completeWith(
        Future(Left(ErrorResponse(400, "All the requested vendors are not configured properly")))
      )
    } else {
      if (availableVendors.size < request.vendors.size) {
        //SOME vendors are not yet configured
        txnLogger.error(s"Some vendors are not yet configured.. Requested Size ${request.vendors.size} Configured Size ${availableVendors.size}")
      }
      val checkSocureId = request.params.businessName.isEmpty && (request.params.dob.isDefined || request.params.nationalId.isDefined)
      val filterVendors = if (checkSocureId) {
        availableVendors
      } else {
        txnLogger.info(s"SOCVL vendor call is being skipped for all business inquiries and for personal inquiries without SSN or DOB")
        availableVendors.filterNot(_.name.equalsIgnoreCase("SOCVL"))
      }

      val updatedVendors =
        if (request.params.nationalId.exists(_.trim.nonEmpty) &&
          request.params.firstName.exists(_.trim.nonEmpty) &&
          request.params.surName.exists(_.trim.nonEmpty)
        ) filterVendors :+ AIGatewayVendorConfig("VRVAL", 4000)
        else filterVendors

      val futureResponses = updatedVendors.map { vendorConfig =>
        val client = vendorClients(vendorConfig.name)

        state.putIfAbsent(
          vendorConfig.name,
          AIVendorResponseWithStatus(data = None, status = AIVendorResponseStatus.PROCESSING)
        )

        client.invoke(vendorConfig.name, transformedRequest).map {
          res =>
            val response = processAoaResponse(res)

            state.compute(
              vendorConfig.name,
              biFunction((key: String, value: AIVendorResponseWithStatus) => AIVendorResponseWithStatus(data = Some(response), status = AIVendorResponseStatus.COMPLETE))
            )

            response
        }
      }

      // Note: flatMap ensures that all futures are completed (sequential execution); we can't plug in early execution logic here
      Future.sequence(futureResponses).flatMap { vendorResponses =>
        txnLogger.debug("Trying with normal processing")
        resultPromise.tryCompleteWith(
          processFinalVendorResponses(request, vendorResponses)(resultPromise)
        )
        resultPromise.future
      }.recover {
        case NonFatal(ex) =>
          txnLogger.error("Exception while getting response from vendor services", ex)
          resultPromise.success(Left(ErrorResponse(199, ex.getMessage)))
      }
    }
  }

  private def processFinalVendorResponses(request: AIGatewayRequest, responses: Seq[AIVendorResponse])(resultPromise: Promise[Either[ErrorResponse, AIGatewayResponse]])(implicit trxId: TrxId): Future[Either[ErrorResponse, AIGatewayResponse]] = {
    val (successResponses, failedResponses) = responses.partition(response => response.metadata.statusCode.equalsIgnoreCase(COMPLETED) || response.metadata.statusCode.equalsIgnoreCase(SKIPPED))
    if (responses.exists(_.metadata.statusCode.equalsIgnoreCase(SKIPPED))) {
      metrics.increment("mblt.fallback.triggered")
    }
    val overallStatus =
      if (successResponses.size == responses.size) SUCCESS
      else if (failedResponses.size == responses.size) FAILURE
      else PARTIAL

    if (overallStatus.equalsIgnoreCase(FAILURE)) {
      resultPromise.completeWith(
        Future(Left(ErrorResponse(500, s"All vendors failed - ${getErrorMessages(failedResponses)}")))
      )
      resultPromise.future
    } else if (overallStatus.equalsIgnoreCase(PARTIAL)) {
      failedResponses.foreach(res => {
        metrics.increment(s"Downstream vendor call failed for - ${res.metadata.name}")
      })
      resultPromise.completeWith(
        Future(Left(ErrorResponse(500, s"Some vendors failed - ${getErrorMessages(failedResponses)}")))
      )
      resultPromise.future
    } else {
      resultPromise.completeWith(
        ruleCodeService.computeVendorRulecodes(request.metadata.transactionId, request.metadata.accountId, responses, request).flatMap { gRc =>
          metrics.timeFuture("reasoncode.resolve.duration") {
            reasonCodeResolver.resolve(gRc.map(_.numericalRulecodes).getOrElse(Map.empty))
          } map {
            case Left(error) => Left(error)
            case Right(reasonCodes) =>
              val asvScore = gRc.get.numericalRulecodes.get(ASV_RULE_CODE)
              val aovScore = gRc.get.numericalRulecodes.get(AOV_RULE_CODE)
              val responseScores = AIGatewayScore.apply(asvScore, aovScore, None, None)
              val filterVRVALResp = filterResponse(responses)
              Right(AIGatewayResponse(
                scores = responseScores,
                status = overallStatus,
                vendorResponses = filterVRVALResp,
                globalRulecodes = gRc,
                reasonCodes = reasonCodes
              ))
          }
        }
      )
      resultPromise.future
    }
  }

  private def filterResponse(responses: Seq[AIVendorResponse]): Seq[AIVendorResponse] = {
    responses.filterNot(_.metadata.name.equalsIgnoreCase("VRVAL"))
  }


  override def processGrpc(request: AIGatewayRequest): Future[Either[ErrorResponse, GatewayResponse]] = {
    implicit val trxId: TrxId = TrxId(request.metadata.transactionId)
    val availableVendors = request.vendors.filter(vendor => vendorClients.contains(vendor.name))
    if (availableVendors.isEmpty) {
      Future(Left(ErrorResponse(400, "All the requested vendors are not configured properly")))
    } else {
      if (availableVendors.size < request.vendors.size) {
        //SOME vendors are not yet configured
        txnLogger.error(s"Some vendors are not yet configured.. Requested Size ${request.vendors.size} Configured Size ${availableVendors.size}")
      }
      val futureResponses = availableVendors.map { vendorConfig =>
        val client = vendorClients(vendorConfig.name)
        client.invoke(vendorConfig.name, request.toVendorRequest).map {
          res => processAoaResponse(res)
        }
      }
      Future.sequence(futureResponses).flatMap { responses =>
        val (successResponses, failedResponses) = responses
          .partition(response => response.metadata.statusCode.equalsIgnoreCase(COMPLETED))
        val overallStatus = if (successResponses.size == responses.size)
          SUCCESS
        else if (failedResponses.size == responses.size) FAILURE
        else PARTIAL

        if (overallStatus.equalsIgnoreCase(FAILURE)) {
          Future(Left(ErrorResponse(500, s"All vendors failed - ${getErrorMessages(failedResponses)}")))
        } else {
          var (statusCode: String, ewsDirectContributor, mbLastSeen, mbDecision) = ("UNKNOWN", "U", "N", "N")
          val ruleCodes: scala.collection.mutable.Map[String, String] = scala.collection.mutable.Map.empty[String, String]
          responses.foreach(res => {
            if (res.result.status.isDefined) {
              if (res.result.status.get.ewsDirectContributor.isDefined) { // BNY
                statusCode = res.result.status.get.code
                res.result.status.get.ewsDirectContributor match {
                  case Some(eDC) => ewsDirectContributor = eDC
                }
              }
              if (res.result.status.get.mbDecisionCode.isDefined) { // MB
                res.result.status.get.mbDecisionCode match {
                  case Some(mBD) => mbDecision = mBD
                }
                res.result.status.get.mbLastSeen match {
                  case Some(mBL) => mbLastSeen = mBL
                }
              }
            }
            if (res.rulecodes.get.categoricalRulecodes.nonEmpty) { // We accumulate all the rule codes, which will be limited in number
              ruleCodes ++= res.rulecodes.get.categoricalRulecodes.filterKeys(ScoreHelper.logicKeys)
            }
          })

          ruleCodeService.computeVendorRulecodes(request.metadata.transactionId, request.metadata.accountId, responses, request).flatMap { gRc =>
            metrics.timeFuture("reasoncode.resolve.duration") {
              reasonCodeResolver.resolve(gRc.map(_.numericalRulecodes).getOrElse(Map.empty))
            } map {
              case Left(error) => Left(error)
              case Right(reasonCodes) =>
                val asvScore = gRc.get.numericalRulecodes.get(ASV_RULE_CODE)
                val aovScore = gRc.get.numericalRulecodes.get(AOV_RULE_CODE)
                val responseScores = GatewayScore.apply(asvScore.get, aovScore.get)
                Right(
                  GatewayResponse.defaultInstance
                    .withStatus(overallStatus)
                    .withVendorResponses(responses.map(_.toGrpc))
                    .withGlobalRulecodes(gRc.map(_.toGrpc).getOrElse(GlobalRulecodes.defaultInstance))
                    .withReasonCodes(reasonCodes.toSeq)
                    .withScore(responseScores))
            }
          }
        }
      }.recover {
        case NonFatal(ex) =>
          txnLogger.error("Exception while getting response from vendor services", ex)
          Left(ErrorResponse(199, ex.getMessage))
      }
    }
  }

  private def getErrorMessages(failedResponses: Seq[AIVendorResponse]): String = {
    failedResponses.mkString(",")
  }

  private def processAoaResponse(res: AIVendorResponse): AIVendorResponse = {
    val metadata = res.metadata
    val error = res.error
    val ruleCodes = res.rulecodes
    val dataAccountStatus = res.result.status
    val dataAccountOwnership: Option[AccountOwnership] = if (res.result.ownership.isDefined) {
      val aosStatusCode = res.result.ownership.get.code
      val aoaDescription = res.result.ownership.get.description
      Some(AccountOwnership.apply(
        code = aoaMatchStatusMap(responseParser.getMatchStatus(aosStatusCode, aoaDescription)),
        description = aoaDescription
      ))
    } else {
      None
    }

    val result = AIVendorResult(dataAccountOwnership, dataAccountStatus)

    AIVendorResponse.apply(metadata = metadata, result = result, error = error, rulecodes = ruleCodes)
  }
}

object AIGatewayVendorServiceImpl {
  private val SUCCESS = "SUCCESS"
  private val FAILURE = "FAILURE"
  private val PARTIAL = "PARTIAL"
  private val COMPLETED = "COMPLETED"
  private val SKIPPED = "SKIPPED"
  private val ASV_RULE_CODE = "GLOBAL.300809"
  private val AOV_RULE_CODE = "GLOBAL.300810"
  private val txnLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
  private val aoaMatchStatusMap = Map(MatchStatus.MATCH -> "Match", MatchStatus.NO_MATCH -> "No Match", MatchStatus.UNKNOWN -> "Unknown", MatchStatus.ERROR -> "Error")
  private val SkippedTriceVendorResponse = (trxId: TrxId) => AIVendorResponse(
    metadata = AIVendorMetadata("TRICE", true, SKIPPED, "", trxId.value),
    result = AIVendorResult(None, None),
    rulecodes = Some(
      AIVendorRuleCodes(
        numericalRulecodes = Map(
          "TRICE.100136" -> 1.0
        ),
        categoricalRulecodes = Map.empty
      )
    )
  )
  private val FlagGroupName = "AccountIntelligenceFeatures"
  private val FlagName = "EnableMicrobiltFallback"

  private def biFunction[A, B, C](f: (A, B) => C): BiFunction[A, B, C] = {
    new BiFunction[A, B, C] {
      override def apply(t: A, u: B): C = f(t, u)
    }
  }
}
