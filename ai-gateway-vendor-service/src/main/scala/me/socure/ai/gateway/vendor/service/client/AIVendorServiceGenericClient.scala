package me.socure.ai.gateway.vendor.service.client

import me.socure.ai.gateway.common.models.AIGatewayVendors
import me.socure.ai.gateway.grpc.resource.{VendorError, VendorMetadata, VendorResponse}

import scala.concurrent.Future

trait AIVendorServiceGenericClient[In, Out] {

  def invoke(vendorName: String, request: In): Future[Out]

}

object AIVendorServiceGenericClient {
  val FAILED: String = "FAILED"

  def getVendorErrorResponse(vendorName: String, errorCode: String, errorDesc: String): VendorResponse = {
    VendorResponse.defaultInstance
      .withMetadata(
        VendorMetadata.defaultInstance
          .withName(AIGatewayVendors.getNameFromValue(vendorName))
          .withSuccess(false)
          .withStatusCode(AIVendorServiceGenericClient.FAILED)
      )
      .withVendorError(
        VendorError.defaultInstance
          .withCode(errorCode)
          .withDescription(errorDesc)
      )
  }
}