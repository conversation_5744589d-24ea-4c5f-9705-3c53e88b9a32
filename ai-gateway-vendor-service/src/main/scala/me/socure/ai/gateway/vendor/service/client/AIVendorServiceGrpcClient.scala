package me.socure.ai.gateway.vendor.service.client

import com.typesafe.config.Config
import io.grpc.{ManagedChannelBuilder, Status, StatusRuntimeException}
import me.socure.ai.gateway.common.models.vendor.{AIVendorRequest, AIVendorResponse}
import me.socure.ai.gateway.grpc.service.AiCommonVendorServiceGrpc
import me.socure.ai.gateway.grpc.service.AiCommonVendorServiceGrpc.AiCommonVendorServiceStub
import me.socure.common.data.core.provider._
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try
import scala.util.control.NonFatal

class AIVendorServiceGrpcClient(aiCommonVendorServiceStub: AiCommonVendorServiceStub)
                               (implicit ec: ExecutionContext) extends AIVendorServiceGenericClient[AIVendorRequest, AIVendorResponse] {

  import AIVendorServiceGrpcClient._

  def invoke(vendorName: String, vendorRequest: AIVendorRequest): Future[AIVendorResponse] = {
    val trxId = TrxId(vendorRequest.transactionId)

    aiCommonVendorServiceStub.validateAccount(vendorRequest.toGrpc)
      .withMetricTags(
        metrics = prefixedMetrics,
        baseTags = MetricTags(
          serviceName = Some("ai-gateway-service"),
          apiName = Some("validateAccount"),
          isInternal = Some(true),
          mSource = Some("server"),
          vendorName = Some(vendorName)
        ),
        onSuccessTags = response => MetricTags(tags = Set(s"responseStatus:${response.vendorError.map(_.code).getOrElse("NA")}"))
      )()
      .recover {
        case sre: StatusRuntimeException =>
          val statusCode = sre.getStatus.getCode.value
          logger.error(s"Grpc StatusRuntimeException [$statusCode] while invoking vendor service for the vendor $vendorName " +
            s"with account id ${vendorRequest.accountId}", sre)(trxId)
          metrics.increment("ai.gateway.vendor.error", s"ex_class:${sre.getClass.getSimpleName}",
            s"ex_msg:${sre.getMessage}", s"ex_code:$statusCode", s"vendor:$vendorName",
            s"accountId:${vendorRequest.accountId}", s"envTypeId:${vendorRequest.environmentTypeId}")
          AIVendorServiceGenericClient.getVendorErrorResponse(vendorName, statusCode.toString, sre.getMessage)
        case NonFatal(ex) =>
          logger.error(s"Exception while invoking vendor service for the vendor $vendorName " +
            s"with account id ${vendorRequest.accountId}", ex)(trxId)
          metrics.increment("ai.gateway.vendor.error", s"ex_class:${ex.getClass.getSimpleName}", s"ex_msg:${ex.getMessage}",
            s"vendor:$vendorName", s"accountId:${vendorRequest.accountId}", s"envTypeId:${vendorRequest.environmentTypeId}")
          AIVendorServiceGenericClient.getVendorErrorResponse(vendorName, Status.UNKNOWN.toString, ex.getMessage)
      }.map(AIVendorResponse.from)
  }
}

object AIVendorServiceGrpcClient {
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass.getSimpleName)

  def apply(config: Config)(implicit ex: ExecutionContext): AIVendorServiceGenericClient[AIVendorRequest, AIVendorResponse] = {
    val useTLS = Try(config.getBoolean("grpc.useTLS")).getOrElse(true)
    val channelBuilder = ManagedChannelBuilder.forAddress(
      config.getString("grpc.host"),
      config.getInt("grpc.port")
    )
    if (!useTLS) {
      channelBuilder.usePlaintext
    }
    new AIVendorServiceGrpcClient(AiCommonVendorServiceGrpc.stub(channelBuilder.build()))
  }
}