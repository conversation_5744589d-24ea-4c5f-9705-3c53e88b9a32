package me.socure.ai.gateway.vendor.service.client

import com.typesafe.config.Config
import dispatch.{Http, url}
import me.socure.ai.gateway.common.models.vendor.{AIVendorRequest, AIVendorResponse}
import me.socure.common.data.core.provider._
import me.socure.common.hmac.filter.HttpWithHmacFactory
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.microservice.client.MicroServiceResponseParser
import me.socure.common.transaction.id.TrxId
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class AIVendorServiceRestClient(http: Http, baseUrl: String, apiName: String)
                               (implicit ec: ExecutionContext) extends AIVendorServiceGenericClient[AIVendorRequest, AIVendorResponse] {

  import AIVendorServiceRestClient._

  private val safeEndpoint = baseUrl.stripSuffix("/")

  def invoke(vendorName: String, vendorRequest: AIVendorRequest): Future[AIVendorResponse] = {
    val requestBody = Serialization.write(vendorRequest)
    val request = url(safeEndpoint + apiName)
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBodyEncoding(Charset.forName("UTF-8")) << requestBody

    val trxId = TrxId(vendorRequest.transactionId)

    val futureResponse = http(request)
      .withMetricTags(
        metrics = prefixedMetrics,
        baseTags = MetricTags(
          serviceName = Some("ai-gateway-service"),
          apiName = Some(apiName),
          isInternal = Some(true),
          mSource = Some("server"),
          vendorName = Some(vendorName)
        ),
        onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
      )()

    futureResponse.map { response =>
      val responseStatus = response.getStatusCode
      val responseBody = response.getResponseBody
      if (responseStatus != 200) {
        logger.error(s"Vendor Failed - StatusCode ${responseStatus} Response $responseBody")(trxId)
      }
      Try {
        MicroServiceResponseParser.parseResponse[AIVendorResponse](responseStatus, responseBody)
      } match {
        case Success(value) => value match {
          case Left(error) => getVendorErrorResponse(vendorName, error.code.toString, error.message)(trxId)
          case Right(res) => res
        }
        case Failure(ex) =>
          metrics.increment("ai.gateway.vendor.error", s"ex_class:${ex.getClass.getSimpleName}", s"ex_msg:${ex.getMessage}",
            s"vendor:$vendorName", s"accountId:${vendorRequest.accountId}", s"envTypeId:${vendorRequest.environmentTypeId}")
          logger.error(s"Exception while invoking vendor service for the vendor $vendorName " +
            s"with account id ${vendorRequest.accountId}", ex)(trxId)
          getVendorErrorResponse(vendorName, "199", ex.getMessage)(trxId)
      }
    }.recover {
      case NonFatal(ex) =>
        logger.error(s"Exception while invoking vendor service for the vendor $vendorName " +
          s"with account id ${vendorRequest.accountId}", ex)(trxId)
        metrics.increment("ai.gateway.vendor.error", s"ex_class:${ex.getClass.getSimpleName}", s"ex_msg:${ex.getMessage}",
          s"vendor:$vendorName", s"accountId:${vendorRequest.accountId}", s"envTypeId:${vendorRequest.environmentTypeId}")
        getVendorErrorResponse(vendorName, "199", ex.getMessage)(trxId)
    }
  }
}

object AIVendorServiceRestClient {
  private implicit val jsonFormats: Formats = DefaultFormats
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass.getSimpleName)

  def getVendorErrorResponse(vendorName: String, errorCode: String, errorDesc: String)(implicit trxId: TrxId): AIVendorResponse = {
    val vendorResponse = AIVendorServiceGenericClient.getVendorErrorResponse(vendorName, errorCode, errorDesc)
    AIVendorResponse.from(vendorResponse)
  }

  def apply(config: Config)(implicit ec: ExecutionContext): AIVendorServiceGenericClient[AIVendorRequest, AIVendorResponse] = {
    val http = if(config.hasPath("hmac")) HttpWithHmacFactory.createInsecure(config = config.getConfig("hmac")) else Http.default
    val apiName = config.getString("apiName")
    new AIVendorServiceRestClient(http, config.getString("endpoint"), apiName)
  }
}
