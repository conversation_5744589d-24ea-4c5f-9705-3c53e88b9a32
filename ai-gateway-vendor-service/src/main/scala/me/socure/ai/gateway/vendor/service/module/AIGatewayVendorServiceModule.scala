package me.socure.ai.gateway.vendor.service.module

import com.google.inject.AbstractModule
import me.socure.ai.gateway.vendor.service.AIGatewayVendorService
import me.socure.ai.gateway.vendor.service.provider.AIGatewayVendorServiceProvider

class AIGatewayVendorServiceModule extends AbstractModule {

  override def configure(): Unit = {
    bind(classOf[AIGatewayVendorService]).toProvider(classOf[AIGatewayVendorServiceProvider]).asEagerSingleton()
  }
}