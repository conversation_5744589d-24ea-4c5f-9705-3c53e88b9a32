package me.socure.ai.gateway.vendor.service.provider

import com.google.inject.{Inject, Provider}
import com.typesafe.config.Config
import me.socure.ai.gateway.common.util.{ResponseParser, Scorer}
import me.socure.ai.gateway.reasoncode.ReasonCodeResolver
import me.socure.ai.gateway.rulecode.RuleCodeService
import me.socure.ai.gateway.vendor.service.client.{AIVendorServiceGrpcClient, AIVendorServiceRestClient}
import me.socure.ai.gateway.vendor.service.{AIGatewayVendorService, AIGatewayVendorServiceImpl}
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory
import org.slf4j.LoggerFactory

import java.util.concurrent.ScheduledThreadPoolExecutor
import scala.collection.JavaConverters.asScalaBufferConverter
import scala.concurrent.ExecutionContext
import scala.util.Try


class AIGatewayVendorServiceProvider @Inject()(config: Config,
                                               ruleCodeService: RuleCodeService,
                                               reasonCodeResolver: ReasonCodeResolver,
                                               responseParser: ResponseParser,
                                               scorer: Scorer
                                              )(implicit executionContext: ExecutionContext)
  extends Provider[AIGatewayVendorService] {

  private val logger = LoggerFactory.getLogger(getClass)

  def get(): AIGatewayVendorService = {
    try {
      new AIGatewayVendorServiceImpl(
        config.getConfigList("vendor.services").asScala.map { config =>
          val useGrpc = Try(config.getBoolean("use.grpc")).getOrElse(true)
          val vendorName = config.getString("vendorName")
          logger.info(s"Initialising Vendor Service Client for $vendorName using grpc [$useGrpc]")
          val genericVendorServiceClient = if (useGrpc)
            AIVendorServiceGrpcClient(config)
          else
            AIVendorServiceRestClient(config)
          vendorName -> genericVendorServiceClient
        }.toMap,
        ruleCodeService,
        reasonCodeResolver,
        responseParser,
        scorer,
        scheduler = new ScheduledThreadPoolExecutor(config.getInt("scheduler.poolSize")),
        dynamicControlCenterV2Evaluate = DynamicControlCenterV2Factory.getEvaluator(config)
      )
    } catch {
      case exception: Exception => throw exception
    }
  }
}
