package me.socure.ai.trice.api

import dispatch.{Http, url}
import me.socure.ai.trice.constants.TriceAPIConstants.TRICE_API_BAD_REQUEST_HTTP_CODE
import me.socure.ai.trice.model.{ExternalAIVendorRequest, TriceAPIResponse}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.json4s.{DefaultFormats, Formats}
import org.json4s.jackson.Serialization
import org.json4s.native.JsonMethods.{compact, parse, render}
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import javax.inject.Inject
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class ExternalAPICaller @Inject()(
                                     http: Http,
                                     endpoint: String
                                 )(implicit ec: ExecutionContext) {
    private implicit val jsonFormats: Formats = DefaultFormats

    val logger: Logger = LoggerFactory.getLogger(getClass)
    private val metrics: Metrics = JavaMetricsFactory.get(getClass)

    def getVendorData(externalAIVendorRequest: ExternalAIVendorRequest): Future[TriceAPIResponse] = {
        val requestBody = Serialization.write(externalAIVendorRequest)
        val request = url(endpoint)
            .POST
            .setContentType("application/json", Charset.forName("UTF-8"))
            .setBodyEncoding(Charset.forName("UTF-8")) << requestBody
        http(request).flatMap {
            response =>
                val statusCode = response.getStatusCode
                if(statusCode == 200) {
                    try {
                        val json = parse(response.getResponseBody)

                        val firstResponse = (json \ "responses")(0)
                        val triceApiStatusCode = (firstResponse \ "status").extract[Int]
                        val responseJValue = (firstResponse \ "response")
                        val responseAsStr = compact(render(responseJValue))
                        val (errorMessage, errorPath) = if(triceApiStatusCode == TRICE_API_BAD_REQUEST_HTTP_CODE) {
                            val firstError = (responseJValue \ "errors")(0)
                            ((firstError \ "message").extractOpt[String], (firstError \ "path").extractOpt[String])
                        }
                        else (None, None)

                        Future(
                            TriceAPIResponse(
                                httpStatusCode = triceApiStatusCode,
                                response = responseAsStr,
                                responseAsJson = Some(responseJValue),
                                errorMessage = errorMessage,
                                errorPath = errorPath
                                ))
                    }
                    catch {
                        case NonFatal(ex) =>
                            logger.error(s"Error while reading response from Trice API call due to, ", ex)
                            Future(TriceAPIResponse(500, "Error in reading Trice response", None))
                    }
                }
                else {
                    logger.error(s"ai-vendor-service API Call failed with http status code ${statusCode} response body ${response.getResponseBody}")
                    Future(TriceAPIResponse(500, "Error in calling ai-vendor-service", None))
                }
        }
    }

}
