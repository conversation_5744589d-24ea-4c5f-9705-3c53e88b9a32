package me.socure.ai.trice.constants

object TriceAPIConstants {
    val TRICE_API_HTTP_SUCCESS_CODES: Set[Int] = Set(200, 201)
    val TRICE_API_BAD_REQUEST_HTTP_CODE = 400
    val PARTY_SUCCESS_STATUS = "ok"
    val PARTY_ON_HOLD_STATUS = "on_hold"
    val TRANSFER_TYPE_RTP = "rtp"
    val TRANSFER_TYPE_RTP_JSON_KEY = "rtp_transfer"
    val TRANSFER_TYPE_RTP_API = "/hub/v1/rtp_transfers/"
    val TRANSFER_TYPE_FEDNOW = "fednow"
    val TRANSFER_TYPE_FEDNOW_JSON_KEY = "fednow_transfer"
    val TRANSFER_TYPE_FEDNOW_API = "/hub/v1/fednow_transfers/"
    val TRANSFER_SUCCESS_STATUS = "completed"
    val POSSIBLE_TRANSFER_FINAL_STATUS: Set[String] = Set(
        "timed_out",
        "failed",
        "rejected",
        "completed",
        "returned"
        )
    val POSSIBLE_TRANSFER_PENDING_STATUS: Set[String] = Set(
        "processing",
        "finalizing",
        "created"
    )
    val TRICE_TRANSFER_STATUS_VS_ASV_DECISION: Map[String, String] = Map(
        "created" -> "unknown",
        "processing" -> "unknown",
        "timed_out" -> "unknown",
        "failed" -> "failed",
        "rejected" -> "failed",
        "finalizing" -> "unknown",
        "completed" -> "accepted",
        "returned" -> "failed"
        )
}
