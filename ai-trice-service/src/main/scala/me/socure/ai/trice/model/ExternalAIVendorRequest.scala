package me.socure.ai.trice.model

case class ExternalAIVendorRequest(
                               transactionId: String,
                               accountId: String,
                               piis: Pii,
                               vendors: Seq[String] = Seq("trice"),
                               vendorConfigs: Map[String, VendorConfig] = Map.empty,
                               maskPiiEnabled: Boolean
                           )

case class Pii(
                  firstName: String,
                  surName: String,
                  entityName: String,
                  dob: String,
                  accountNumber: String,
                  routingNumber: String,
                  accountCountry: String,
                  streetAddress: String,
                  streetAddress2: String,
                  city: String,
                  state: String,
                  country: String,
                  zipCode: String
              )

case class VendorConfig(
                           uri: String,
                           http_method: String,
                           template: String,
                           headerTemplate: Option[String] = None,
                           memo: String,
                           partyId: String,
                           ultimateSendingPartyId: String,
                           accountHolderType: String = "",
                           domestic: Boolean = true,
                           waitForMS: Long = 4000L
                       )
