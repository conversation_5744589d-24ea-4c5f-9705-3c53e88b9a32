package me.socure.ai.trice.model

case class PartyCreateFailedResponse(
                                        partyId: Option[String] = Some(""),
                                        status: Option[String] = Some(""),
                                        response: Option[String] = Some(""),
                                        errorMessage: Option[String] = None,
                                        errorPath: Option[String] = None,
                                        holdDetailsMessage: Option[String] = None,
                                        holdDetailsReason: Option[String] = None
                                    ) {
    def toTriceIntelligenceResponse: TriceIntelligenceResponse = {
        TriceIntelligenceResponse(
            statusCode = 400,
            lastApiCalled = Some("createParty"),
            errorMessage = this.errorMessage,
            errorPath = this.errorPath,
            isAccountValidated = Some(false),
            isRTPTransferEnabled = Some(true),
            isFedNowTransferEnabled = None,
            isRoutingNumberUnavailable = Some(false),
            partyId = this.partyId,
            ultimateSendingPartyId = None,
            transferId = Some(""),
            pennyDropMode = Some(""),
            pennyDropStatus = Some(""),
            holdDetailsMessage = this.holdDetailsMessage,
            holdDetailsReason = this.holdDetailsReason,
            stateDetailsMessage = Some(""),
            stateDetailsReason = Some(""),
            stateDetailsReasonCode = Some("")
            )
    }
}
