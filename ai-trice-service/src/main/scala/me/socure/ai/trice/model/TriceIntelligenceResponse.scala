package me.socure.ai.trice.model

import me.socure.ai.gateway.common.models.vendor.{AIVendorMetadata, AIVendorResponse, AIVendorResult}
import me.socure.ai.trice.constants.TriceAPIConstants.{TRANSFER_SUCCESS_STATUS, TRICE_API_HTTP_SUCCESS_CODES}

case class TriceIntelligenceResponse(
                                        statusCode: Int = 500,
                                        lastApiCalled: Option[String] = None,
                                        response: Option[String] = None, //FromAnyAPI
                                        errorMessage: Option[String] = None, //FromAnyAPI
                                        errorPath: Option[String] = None, //FromAnyAPI
                                        isAccountValidated: Option[Boolean] = Some(false), //FromTransfer
                                        isRTPTransferEnabled: Option[Boolean] = Some(true), //FromInternalFileLookup
                                        isFedNowTransferEnabled: Option[Boolean] = Some(true), //FromInternalFileLookup
                                        isRoutingNumberUnavailable: Option[Boolean] = Some(false), //FromInternalFileLookup
                                        partyId: Option[String] = Some(""), //FromTransfer
                                        ultimateSendingPartyId: Option[String] = Some(""), //FromTransfer
                                        transferId: Option[String] = Some(""), //FromTransfer
                                        pennyDropMode: Option[String] = Some("rtp"), //FromTransfer
                                        pennyDropStatus: Option[String] = Some(""), //FromTransfer
                                        holdDetailsMessage: Option[String] = Some(""), //ForParty
                                        holdDetailsReason: Option[String] = Some(""), //ForParty
                                        stateDetailsMessage: Option[String] = Some(""), //FromTransfer
                                        stateDetailsReason: Option[String] = Some(""), //FromTransfer
                                        stateDetailsReasonCode: Option[String] = Some(""), //FromTransfer
                                        lastSeenDate: Option[String] = Some("")
                                    ) {
    def toAIVendorResponse(txnId: String): AIVendorResponse = {
        //Setting metadataStatusCode as COMPLETED so that even if Trice API fails, whole ID+ txn isn't failed.
        //        val metadataStatusCode =
        //            if(TRICE_API_HTTP_SUCCESS_CODES.contains(this.statusCode)) "COMPLETED"
        //            else "FAILED"
        //        val metadataSuccess =
        //            if("COMPLETED".equals(metadataStatusCode)) true
        //            else false
        val metadataStatusCode = "COMPLETED"
        val metadataSuccess = true

        val vendorRefId = this.transferId.getOrElse("")
        val metadata = AIVendorMetadata.apply(
            name = me.socure.ai.gateway.grpc.resource.Name.TRICE.name,
            success = metadataSuccess,
            statusCode = metadataStatusCode,
            vendorReferenceId = vendorRefId,
            transactionId = txnId
            )

        val result = AIVendorResult.apply(
            status = None,
            ownership = None
            )

        AIVendorResponse.apply(metadata, result)
    }
}
