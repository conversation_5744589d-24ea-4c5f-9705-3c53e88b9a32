package me.socure.ai.trice.model

case class TriceUltimateSendingPartyCreateOrUpdateRequest(
                                                             partyId: Option[String] = None,
                                                             accountId: String,
                                                             depositorName: String,
                                                             physicalAddress: String,
                                                             physicalAddress2: Option[String] = None,
                                                             city: String,
                                                             state: String,
                                                             zip: String,
                                                             country: String
                                                         ) {
    override def toString: String = {
        "TriceUltimateSendingPartyCreateOrUpdateRequest [partyId=" + partyId +
            ", accountId=" + accountId +
            ", depositorName=" + depositorName +
            ", physicalAddress=" + physicalAddress +
            ", physicalAddress2=" + physicalAddress2 +
            ", city=" + city +
            ", state=" + state +
            ", zip=" + zip +
            ", country=" + country +
            "]"
    }
}
