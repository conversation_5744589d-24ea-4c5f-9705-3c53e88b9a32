package me.socure.ai.trice.module

import com.google.inject.name.Named
import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import dispatch.Http
import me.socure.ai.gateway.common.cache.impl.DynamoCacheService
import me.socure.ai.trice.api.ExternalAPICaller
import me.socure.ai.trice.model.TriceRoutingNumberInfo
import me.socure.ai.trice.service.{RuleCodeService, TriceVerificationService}
import me.socure.common.closeable.UsingCloseable

import java.io.{BufferedInputStream, InputStreamReader}
import java.util.zip.GZIPInputStream
import scala.util.{Failure, Success, Try}
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.slf4j.{Logger, LoggerFactory}
import software.amazon.awssdk.services.dynamodb.DynamoDbClient

import scala.collection.JavaConverters.asScalaIteratorConverter
import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

class TriceModule extends AbstractModule {

    val logger: Logger = LoggerFactory.getLogger(getClass)

    @Provides
    @Singleton
    @Named("TriceKnownRoutingNumberLookup")
    private def getTriceRoutingNumberInfo: Map[String, TriceRoutingNumberInfo] = {
        Try {
            UsingCloseable.using(new GZIPInputStream(new BufferedInputStream(getClass.getResourceAsStream("/routing_numbers.csv.gz")))) {
                source: GZIPInputStream =>
                    val csvParser = new CSVParser(new InputStreamReader(source), CSVFormat.DEFAULT.withFirstRecordAsHeader())
                    csvParser.iterator().asScala.map(parseRecord).toMap
            }
        }
        match {
            case Success(value) => value
            case Failure(exception) =>
                logger.error("Trice Logger : Error in loading the Routing number data from csv due to ", exception)
                Map.empty
        }
    }

    @Provides
    @Singleton
    private def getTriceVerificationService(config: Config, @Named("TriceKnownRoutingNumberLookup") triceKnownRoutingNumbers: Map[String, TriceRoutingNumberInfo])(implicit ec: ExecutionContext): TriceVerificationService = {
        val http = Http.default
        val endpoint = config.getString("ai.vendor.service.api")
        val maxPollCount = Try(config.getInt("trice.maxPollCount")).getOrElse(4)
        val maxPollTimeoutDuration: FiniteDuration = (Try(config.getInt("trice.maxPollTimeoutInMillis")).getOrElse(4000)).milliseconds
        val sleepBetweenPollInMillis = Try(config.getInt("trice.sleepBetweenPollInMillis")).getOrElse(4000)
        new TriceVerificationService(triceKnownRoutingNumbers, new ExternalAPICaller(http, endpoint), new RuleCodeService, new DynamoCacheService(config.getConfig("cache.dynamo"), DynamoDbClient.builder().build()), maxPollCount, maxPollTimeoutDuration, sleepBetweenPollInMillis)
    }

    private def parseRecord(record: org.apache.commons.csv.CSVRecord): (String, TriceRoutingNumberInfo) = {
        val abaRoutingNumber = record.get(0)
        val triceRoutingInfo = TriceRoutingNumberInfo(
            aba_routing_number = abaRoutingNumber,
            rtp_receive = record.get(1).toBoolean,
            rtp_receive_rfp = record.get(2).toBoolean,
            fednow_receive = record.get(3).toBoolean,
            fednow_receive_rfp = record.get(4).toBoolean
            )
        (abaRoutingNumber, triceRoutingInfo)
    }
}
