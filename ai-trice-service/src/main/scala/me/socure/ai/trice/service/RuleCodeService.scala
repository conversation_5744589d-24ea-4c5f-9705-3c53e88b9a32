package me.socure.ai.trice.service

import me.socure.ai.gateway.common.models.vendor.{AIVendorResponse, AIVendorRuleCodes}
import me.socure.ai.trice.model.TriceIntelligenceResponse
import com.socure.domain.socure.scoring.RuleCodes._
import me.socure.ai.trice.constants.TriceAPIConstants.TRICE_TRANSFER_STATUS_VS_ASV_DECISION

class RuleCodeService {

    def computeVendorRuleCodes(aiVendorResponse: AIVendorResponse, triceIntelligenceResponse: TriceIntelligenceResponse): AIVendorRuleCodes = {
        val isRoutingNumberSupported = !triceIntelligenceResponse.isRoutingNumberUnavailable.contains(true)
        val transferStatusOpt = triceIntelligenceResponse.pennyDropStatus
        val stateDetailsReason = triceIntelligenceResponse.stateDetailsReason.getOrElse("")
        val stateDetailsReasonCode = triceIntelligenceResponse.stateDetailsReasonCode.getOrElse("")
        val asvDecision = transferStatusOpt match {
            case Some(transferStatus) if (transferStatus.trim.nonEmpty) => TRICE_TRANSFER_STATUS_VS_ASV_DECISION.getOrElse(transferStatus, "unknown")
            case  _ => "unknown"
        }

        val numericalRuleCodes: Map[String, Double] = Map(TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_ROUTING_SUPPORT.code.toString -> boolToDouble(isRoutingNumberSupported))
        val baseCategoricalRuleCodes: Map[String, String] = Map(
            TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_ASV_STATUS_CODE.code.toString -> transferStatusOpt.getOrElse(""),
            TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_ASV_REJECT.code.toString -> stateDetailsReason,
            TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_ASV_REJECT_CODE.code.toString -> stateDetailsReasonCode,
            TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_ASV_DECISION.code.toString -> asvDecision
            )

        val transactionDateEntry: Option[(String, String)] =
            triceIntelligenceResponse.lastSeenDate.filter(_.nonEmpty)
              .map(date => TRICE_ASV_STATUS_CODE.source.name + "." + TRICE_TRANSACTION_DATE.code.toString -> date)

        val categoricalRuleCodes: Map[String, String] = baseCategoricalRuleCodes ++ transactionDateEntry.toMap

        AIVendorRuleCodes(numericalRulecodes = numericalRuleCodes, categoricalRulecodes = categoricalRuleCodes)
    }

    def boolToDouble(boolean: Boolean) = {
        if(boolean) 1.0
        else 0.0
    }
}
