package me.socure.ai.trice.service

import com.google.common.base.Strings
import me.socure.ai.gateway.common.cache.{CacheService, CacheValue}
import me.socure.ai.gateway.common.models.vendor.{AIVendorRequest, AIVendorResponse, AIVendorRuleCodes}
import me.socure.ai.trice.api.ExternalAPICaller
import me.socure.ai.trice.constants.TriceAPIConstants._
import me.socure.ai.trice.model._
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import org.joda.time.DateTime
import org.joda.time.format.{DateTimeFormat, DateTimeFormatter, ISODateTimeFormat}
import org.json4s.JsonAST.JValue
import org.json4s._

import java.util.UUID
import java.util.concurrent.TimeoutException
import javax.inject.{Inject, Named}
import scala.annotation.tailrec
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class TriceVerificationService @Inject()(
                                            @Named("TriceKnownRoutingNumberLookup") triceKnownRoutingNumbers: Map[String, TriceRoutingNumberInfo],
                                            externalAPICaller: ExternalAPICaller,
                                            ruleCodeService: RuleCodeService,
                                            cacheService: CacheService,
                                            maxPollCount: Int,
                                            maxPollTimeoutDuration: Duration,
                                            sleepBetweenPollInMillis: Int
                                        )(implicit ec: ExecutionContext) {
    private implicit val jsonFormats: Formats = DefaultFormats
    val logger = TransactionAwareLoggerFactory.getLogger(getClass)
    private val metrics: Metrics = JavaMetricsFactory.get(getClass)
    private val dobFormat = DateTimeFormat.forPattern("yyyyMMdd")
    val dobFormats: List[DateTimeFormatter] = List(
        ISODateTimeFormat.date(),
        ISODateTimeFormat.dateTime(),
        DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ"),
        DateTimeFormat.forPattern("yyyy-MM-dd"),
        DateTimeFormat.forPattern("yyyyMMdd"),
        DateTimeFormat.forPattern("yyyy/MM/dd"),
        DateTimeFormat.forPattern("MM/dd/yyyy")
        ).map(_.withZoneUTC())

    def process(vendorRequest: AIVendorRequest): Future[Either[ErrorResponse, AIVendorResponse]] = {
        implicit val txnId = TrxId(vendorRequest.transactionId)
        val baseExternalAIVendorRequest = convertToExternalAIVendorRequest(vendorRequest)
        val cacheKey = generateCacheKey(baseExternalAIVendorRequest)
        // Non-blocking way to call the cache service for now since using AsyncClient needs scala conversions for CompletableFuture
        val cacheResponseFuture = Future.apply(cacheService.get(cacheKey))
        val triceIntelligenceResponse = for {
            cacheResponse <- cacheResponseFuture
            response <- cacheResponse map { value =>
                val initialTriceIntelligenceResponse = TriceIntelligenceResponse(
                    statusCode = 201, //Need to manually ensure this is set so that polling conditions are met
                    transferId = Some(value.transferId),
                    pennyDropMode = Some(value.transferType)
                    //                pennyDropStatus = value.transferStatus
                )
                Future.apply(getTransferDetailsIntelligenceWithPolling(baseExternalAIVendorRequest, initialTriceIntelligenceResponse, Some(cacheKey)))
            } getOrElse {
                triceKnownRoutingNumbers.get(baseExternalAIVendorRequest.piis.routingNumber) match {
                    case Some(triceRoutingNumberInfo) if (triceRoutingNumberInfo.rtp_receive || triceRoutingNumberInfo.fednow_receive) =>
                        getIntelligenceThroughPennyDrop(baseExternalAIVendorRequest)
                    case Some(triceRoutingNumberInfo) if (!triceRoutingNumberInfo.rtp_receive && !triceRoutingNumberInfo.fednow_receive) =>
                        metrics.increment(s"trice.rtp.receive.disabled.in.internal.data")
                        logger.info(s"Trice Logger : RTP/FedNow Receive not enabled for the provided routing number in internal routing number source data for TxnID: ${txnId}")
                        Future.successful(
                            TriceIntelligenceResponse(
                                statusCode = 200,
                                lastApiCalled = Some("internalAbaRoutingSupport"),
                                errorMessage = Some("RTPTransfer not available for Routing number"),
                                isRTPTransferEnabled = Some(triceRoutingNumberInfo.rtp_receive),
                                isFedNowTransferEnabled = Some(triceRoutingNumberInfo.fednow_receive)
                            )
                        )
                    case _ =>
                        metrics.increment(s"trice.routing.number.not.found.in.internal.data")
                        logger.info(s"Trice Logger : Routing number not found in the internal data for TxnID: ${txnId}")
                        Future.successful(
                            TriceIntelligenceResponse(
                                statusCode = 200,
                                lastApiCalled = Some("internalAbaRoutingSupport"),
                                errorMessage = Some("Routing number not supported"),
                                isRoutingNumberUnavailable = Some(true)
                            )
                        )
                }
            }
        } yield response
        triceIntelligenceResponse.map {
            (response) =>
                logger.info(s"Trice Logger : Received Intelligence from Trice for TxnID: ${txnId}")
                if(response.isRTPTransferEnabled.contains(true) && response.isRoutingNumberUnavailable.contains(false)) {
                    val isAccountValidated = response.isAccountValidated.getOrElse(false)
                    metrics.increment(s"trice.call.validation.status.$isAccountValidated")
                    response.pennyDropStatus match {
                        case Some(status) if !Strings.isNullOrEmpty(status) => metrics.increment(s"trice.call.penny.drop.status.$status")
                        case _ => //Do nothing
                    }
                }
                val vendorResponse = response.toAIVendorResponse(vendorRequest.transactionId)
                val aiVendorRuleCodes = ruleCodeService.computeVendorRuleCodes(vendorResponse, response)
                Right(
                    AIVendorResponse.apply(
                        vendorResponse.metadata,
                        vendorResponse.result,
                        vendorResponse.error,
                        Some(AIVendorRuleCodes.apply(aiVendorRuleCodes.numericalRulecodes, aiVendorRuleCodes.categoricalRulecodes))
                        )
                    )
        }

    }

    def convertToExternalAIVendorRequest(vendorRequest: AIVendorRequest): ExternalAIVendorRequest = {
        val pii = Pii(
            firstName = vendorRequest.firstName.getOrElse(""),
            surName = vendorRequest.surName.getOrElse(""),
            entityName = vendorRequest.businessName.getOrElse(""),
            dob = vendorRequest.dob.flatMap(extractDob).map(dobFormat.print).getOrElse(""),
            accountNumber = vendorRequest.accountNumber,
            routingNumber = vendorRequest.routingNumber,
            accountCountry = vendorRequest.accountCountry.getOrElse(""),
            streetAddress = vendorRequest.physicalAddress.getOrElse(""),
            streetAddress2 = vendorRequest.physicalAddress2.getOrElse(""),
            city = vendorRequest.city.getOrElse(""),
            state = vendorRequest.state.getOrElse(""),
            country = vendorRequest.country.getOrElse(""),
            zipCode = vendorRequest.zip.getOrElse("")
        )
        val vendorConfig = Map(
            "trice" -> VendorConfig(
                uri = "",
                http_method = "",
                template = "",
                memo = vendorRequest.memo.getOrElse(""),
                partyId = "",
                ultimateSendingPartyId = vendorRequest.ultimateSendingPartyId.getOrElse("")
                ))
        ExternalAIVendorRequest(
            transactionId = vendorRequest.transactionId,
            accountId = vendorRequest.accountId.toString,
            piis = pii,
            vendorConfigs = vendorConfig,
            maskPiiEnabled = vendorRequest.maskPii
        )
    }

    def getIntelligenceThroughPennyDrop(baseExternalAIVendorRequest: ExternalAIVendorRequest)(implicit trxId: TrxId): Future[TriceIntelligenceResponse] = {
        metrics.increment(s"trice.calls.count")
        logger.info(s"Trice Logger : Proceeding to retrieve Trice intelligence for TxnID : ${baseExternalAIVendorRequest.transactionId}")
        // Trice Wait Header ensures that P95 of the request is good enough for most requests to reach completed status in <3s so caching pending statuses here should only reflect a small amount of transactions
        smartVerify(baseExternalAIVendorRequest) map { triceIntelligenceResponse =>
            //Scenario 1: Response successful but penny drop pending
            if (triceIntelligenceResponse.pennyDropStatus.exists(POSSIBLE_TRANSFER_PENDING_STATUS.contains)) {
                //Cache the result and proceed to polling.
                cacheService.set(
                    id = generateCacheKey(baseExternalAIVendorRequest),
                    value = CacheValue(
                        transferId = triceIntelligenceResponse.transferId.getOrElse(""),
                        transferType = triceIntelligenceResponse.pennyDropMode.getOrElse(""),
                        transferStatus = triceIntelligenceResponse.pennyDropStatus
                    )
                )
                getTransferDetailsIntelligenceWithPolling(baseExternalAIVendorRequest, triceIntelligenceResponse)
            }
            //Scenario 2: Response is already either an error or is in a "final" state; Should not be cached since we cannot cache entire response from Trice due to legal reasons
            else if(!TRICE_API_HTTP_SUCCESS_CODES.contains(triceIntelligenceResponse.statusCode) || triceIntelligenceResponse.pennyDropStatus.exists(POSSIBLE_TRANSFER_FINAL_STATUS.contains)) {
//                if (triceIntelligenceResponse.pennyDropStatus.contains(TRANSFER_SUCCESS_STATUS)) {
//                    cacheService.set(
//                        id = s"${baseExternalAIVendorRequest.piis.routingNumber}-${baseExternalAIVendorRequest.piis.accountNumber}",
//                        value = CacheValue(
//                            transferId = triceIntelligenceResponse.transferId.getOrElse(""),
//                            transferType = triceIntelligenceResponse.pennyDropMode.getOrElse(""),
//                            transferStatus = triceIntelligenceResponse.pennyDropStatus
//                        )
//                    )
//                }
                triceIntelligenceResponse
            //Scenario 3: Should ideally no longer be invoked for scenarios; Left for safety reasons
            } else
                getTransferDetailsIntelligenceWithPolling(baseExternalAIVendorRequest, triceIntelligenceResponse)
        }
    }

    /**
     * Checks transfer status API once
     * @param baseExternalAIVendorRequest
     * @param currentTriceIntelligenceResponse
     * @param trxId
     * @return
     */
    private def getTransferDetailsIntelligenceWithoutPolling(
                                                  baseExternalAIVendorRequest: ExternalAIVendorRequest,
                                                  currentTriceIntelligenceResponse: TriceIntelligenceResponse
                                                )(implicit trxId: TrxId): TriceIntelligenceResponse = {
        val txnId = baseExternalAIVendorRequest.transactionId
        val pennyDropMode = currentTriceIntelligenceResponse.pennyDropMode
        var triceIntelligenceResponse = currentTriceIntelligenceResponse

        val retrieveTransferFuture = retrieveSmartTransferDetails(baseExternalAIVendorRequest, triceIntelligenceResponse)
        triceIntelligenceResponse = Try(Await.result(retrieveTransferFuture, maxPollTimeoutDuration)) match {
            case Success(currentTriceIntelligenceResponse) => {
                // We need not cache anything here; if we cache completed status, we cannot cache the entire response
                currentTriceIntelligenceResponse
            }
            case Failure(_: TimeoutException) => {
                metrics.increment(s"trice.${pennyDropMode.get}.transfer.poll.timeout")
                logger.info(s"Trice Logger : ${pennyDropMode.get} Transfer poll request timed out for TxnID: ${txnId}")
                // if cache key is defined, do nothing since transfer id is already cached. Else cache the pending status transfer id
                //                    cacheIfNeeded(baseExternalAIVendorRequest, triceIntelligenceResponse, cacheKey)
                triceIntelligenceResponse
            }
            case Failure(exception) =>
                metrics.increment(s"trice.${pennyDropMode.get}.transfer.poll.failed.unknown.error")
                logger.error(s"Trice Logger : Error occurred in polling ${pennyDropMode.get} Transfer for TxnID: ${txnId} due to ", exception)
                // should we invalidate here
                triceIntelligenceResponse
        }
        triceIntelligenceResponse
    }

    def getTransferDetailsIntelligenceWithPolling(
                                                   baseExternalAIVendorRequest: ExternalAIVendorRequest,
                                                   currentTriceIntelligenceResponse: TriceIntelligenceResponse,
                                                   cacheKey: Option[String] = None
                                                 )(implicit trxId: TrxId): TriceIntelligenceResponse = {
        val txnId = baseExternalAIVendorRequest.transactionId
        var currentPollCount = 0
        var triceIntelligenceResponse = currentTriceIntelligenceResponse
        val pennyDropMode = currentTriceIntelligenceResponse.pennyDropMode
        val startTime = System.nanoTime()

        while(currentPollCount < maxPollCount && TRICE_API_HTTP_SUCCESS_CODES.contains(triceIntelligenceResponse.statusCode) && !triceIntelligenceResponse.pennyDropStatus.exists(POSSIBLE_TRANSFER_FINAL_STATUS.contains)) {
            val retrieveTransferFuture = retrieveSmartTransferDetails(baseExternalAIVendorRequest, triceIntelligenceResponse)
            triceIntelligenceResponse = Try(Await.result(retrieveTransferFuture, maxPollTimeoutDuration)) match {
                case Success(currentTriceIntelligenceResponse) => {
                    // We need not cache anything here; if we cache completed status, we cannot cache the entire response which is needed for rulecode generation
                    currentTriceIntelligenceResponse
                }
                case Failure(_: TimeoutException) => {
                    metrics.increment(s"trice.${pennyDropMode.get}.transfer.poll.timeout")
                    logger.info(s"Trice Logger : ${pennyDropMode.get} Transfer poll request timed out for TxnID: ${txnId}")
                    // if cache key is defined, do nothing since transfer id is already cached. Else cache the pending status transfer id
//                    cacheIfNeeded(baseExternalAIVendorRequest, triceIntelligenceResponse, cacheKey)
                    triceIntelligenceResponse
                }
                case Failure(exception) =>
                    metrics.increment(s"trice.${pennyDropMode.get}.transfer.poll.failed.unknown.error")
                    logger.error(s"Trice Logger : Error occurred in polling ${pennyDropMode.get} Transfer for TxnID: ${txnId} due to ", exception)
                    // should we invalidate here
                    triceIntelligenceResponse
            }
            currentPollCount += 1
            // Check if time limit is exceeded
            val elapsedTime = (System.nanoTime() - startTime).nanos
            if(elapsedTime >= maxPollTimeoutDuration) {
                metrics.increment(s"trice.${pennyDropMode.get}.transfer.final.status.not.obtained.within.threshold")
                logger.info(s"Trice Logger : ${pennyDropMode.get} Transfer didn't attain a final status within ${maxPollTimeoutDuration.toSeconds} seconds for TxnID: ${txnId}")
                //Revisit Caching Here; We can also consider invalidating the cache to prevent stuck transactions
                return triceIntelligenceResponse
            }
            Thread.sleep(sleepBetweenPollInMillis)
        }
        // We should also check status of the Trice Response here; Potential Bug
        if(currentPollCount <= maxPollCount) {
            metrics.increment(s"trice.${pennyDropMode.get}.transfer.final.status.reached.in.poll.$currentPollCount")
            logger.info(s"Trice Logger : ${pennyDropMode.get} Transfer final status reached in pollCount: $currentPollCount for TxnID: ${txnId}")
            //already invalidating cache in case of successful result; should we invalidate cache here in case a pending one was kept but should we consider the overall timeout <3s?
        }
        // This is fine
        else if(currentPollCount > maxPollCount) {
            metrics.increment(s"trice.${pennyDropMode.get}.transfer.final.status.not.reached.within.maximum.polls")
            logger.info(s"Trice Logger : Transfer final status not reached within maximum poll count : ${currentPollCount} for TxnID: ${txnId}")
//            cacheIfNeeded(baseExternalAIVendorRequest, triceIntelligenceResponse, cacheKey)
        }
        triceIntelligenceResponse
    }

    def createParty(baseExternalAIVendorRequest: ExternalAIVendorRequest)(implicit trxId: TrxId): Future[Either[PartyCreateFailedResponse,
      PartyCreateSuccessResponse]] = {
        val txnId = baseExternalAIVendorRequest.transactionId
        val vendorConfigs =
            Map(
                "trice" -> VendorConfig(
                    uri = "/hub/v1/parties",
                    http_method = "post",
                    template = "trice.body.create-party",
                    memo = "",
                    partyId = "",
                    ultimateSendingPartyId = ""
                    ))
        val externalAIVendorRequest = baseExternalAIVendorRequest.copy(vendorConfigs = vendorConfigs)
        val createPartyResponseFuture = externalAPICaller.getVendorData(externalAIVendorRequest)
        createPartyResponseFuture.map {
            createPartyResponse =>
                if(TRICE_API_HTTP_SUCCESS_CODES.contains(createPartyResponse.httpStatusCode)) {
                    createPartyResponse.responseAsJson match {
                        case Some(partyCreateResponseJson) =>
                            val partyStatus = (partyCreateResponseJson \ "status").extract[String]
                            val partyId = (partyCreateResponseJson \ "id").extract[String]
                            if(PARTY_SUCCESS_STATUS.equalsIgnoreCase(partyStatus))
                                Right(PartyCreateSuccessResponse(partyId = partyId, status = partyStatus))
                            else {
                                if(PARTY_ON_HOLD_STATUS.equalsIgnoreCase(partyStatus)) {
                                    val holdDetails = (partyCreateResponseJson \ "hold_details")
                                    val holdMessage = (holdDetails \ "message").extractOpt[String]
                                    val holdReason = (holdDetails \ "reason").extractOpt[String]
                                    Left(PartyCreateFailedResponse(partyId = Some(partyId), status = Some(partyStatus), holdDetailsMessage = holdMessage, holdDetailsReason = holdReason))
                                }
                                else Left(PartyCreateFailedResponse(partyId = Some(partyId), status = Some(partyStatus), errorMessage = Some("Unhandled Party status")))
                            }
                        case _ => {
                            //For 201 status, response would be there. This case won't happen.
                            metrics.increment("trice.create.party.response.parsing.failed")
                            logger.info(s"Trice Logger : Create Party Response parsing failed for TxnID: ${txnId}")
                            Left(PartyCreateFailedResponse())
                        }
                    }
                }
                else {
                    metrics.increment("trice.create.party.failed")
                    logger.info(s"Trice Logger : Create Party Request failed with http status code, ${createPartyResponse.httpStatusCode} for TxnID: ${txnId}")
                    Left(PartyCreateFailedResponse(response = Some(createPartyResponse.response), errorMessage = createPartyResponse.errorMessage, errorPath = createPartyResponse.errorPath))
                }
        }
    }

    def createRTPTransfer(baseExternalAIVendorRequest: ExternalAIVendorRequest, partyId: String)(implicit trxId: TrxId): Future[TriceIntelligenceResponse] = {
        val ultimateSendingPartyId = baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId
        val vendorConfigs =
            Map(
                "trice" -> VendorConfig(
                    uri = "/hub/v1/rtp_transfers",
                    http_method = "post",
                    template = "trice.body.create-rtp-transfer",
                    headerTemplate = Some("trice.header.transfer"),
                    memo = baseExternalAIVendorRequest.vendorConfigs("trice").memo,
                    partyId = partyId,
                    ultimateSendingPartyId = baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId
                    ))
        val externalAIVendorRequest = baseExternalAIVendorRequest.copy(vendorConfigs = vendorConfigs)
        val createRtpTransferResponseFuture = externalAPICaller.getVendorData(externalAIVendorRequest)
        createRtpTransferResponseFuture.map {
            createRTPTransferResponse => parseSmartTransferResponse(baseExternalAIVendorRequest = baseExternalAIVendorRequest, smartTransferResponse = createRTPTransferResponse, partyId = Some(partyId), ultimateSendingPartyId = Some(ultimateSendingPartyId), uri = "/hub/v1/rtp_transfers")
        }
    }

    private def smartVerify(baseExternalAIVendorRequest: ExternalAIVendorRequest)(implicit trxId: TrxId) = {
        val txnId = baseExternalAIVendorRequest.transactionId
        val vendorConfigs =
            Map(
                "trice" -> VendorConfig(
                    uri = "/hub/v1/smart_verify",
                    http_method = "post",
                    template = "trice.body.create-smart-verify-transfer",
                    headerTemplate = Some("trice.header.smart-verify"),
                    memo = baseExternalAIVendorRequest.vendorConfigs("trice").memo,
                    partyId = "", //This will be empty since smarty_verify endpoint will take care of creating the party and then getting rtp transfer details
                    ultimateSendingPartyId = baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId,
                    accountHolderType = accountHolderType(baseExternalAIVendorRequest.piis),
                    domestic(baseExternalAIVendorRequest.piis),
                    waitForMS = 4000L
                ))
        val externalAIVendorRequest = baseExternalAIVendorRequest.copy(vendorConfigs = vendorConfigs)
        val smartyVerifyResponseFuture = metrics.timeFuture("trice.calls.smart.verify") {
            externalAPICaller.getVendorData(externalAIVendorRequest)
        }
        smartyVerifyResponseFuture map { smartVerifyResponse =>
            smartVerifyResponse.responseAsJson match {
                case Some(smartVerifyResponseJson) =>
                    val (pennyDropMode, transferJsonKey) = (smartVerifyResponseJson \ TRANSFER_TYPE_RTP_JSON_KEY, smartVerifyResponseJson \ TRANSFER_TYPE_FEDNOW_JSON_KEY) match {
                        case (_, JNothing)        => (Some(TRANSFER_TYPE_RTP), TRANSFER_TYPE_RTP_JSON_KEY)
                        case (JNothing, _)        => (Some(TRANSFER_TYPE_FEDNOW), TRANSFER_TYPE_FEDNOW_JSON_KEY)
                        case (JNothing, JNothing) =>
                            logger.info(s"Trice Logger : Transfer Response parsing failed for TxnID: ${txnId} : Neither RTP Transfer nor FedNow Transfer Details are present in the response")
                            (None, "")
                        case _ => (None, "")
                    }

                    val smartTransferResponseJson = smartVerifyResponseJson \ transferJsonKey
                    val transferStatus = (smartTransferResponseJson \ "status").extractOpt[String]
                    val isAccountValidated = transferStatus.exists(TRANSFER_SUCCESS_STATUS.equalsIgnoreCase)
                    val transferId = (smartTransferResponseJson \ "id").extractOpt[String]
                    val stateDetails = (smartTransferResponseJson \ "state_details")
                    val (stateMessage, stateReason, stateReasonCode) = stateDetails match {
                        case jVal: JValue => ((stateDetails \ "message").extractOpt[String], (stateDetails \ "reason").extractOpt[String], (stateDetails \ "reason_code").extractOpt[String])
                        case _ => (None, None, None)
                    }

                    val partyId = (smartVerifyResponseJson \ "new_party" \ "id").extractOpt[String]

                    if (transferId.isEmpty) {
                        TriceIntelligenceResponse(
                            statusCode = 500,
                            lastApiCalled = Some("smart_verify"),
                            response = Some(smartTransferResponseJson.toString),
                            ultimateSendingPartyId = Some(baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId),
                            pennyDropMode = pennyDropMode,
                            errorMessage = Some("Penny Drop Transfer ID Not Received from External Vendor Call")
                        )
                    } else {
                        TriceIntelligenceResponse(
                            statusCode = 200,
                            lastApiCalled = Some("smart_verify"),
                            response = Some(smartTransferResponseJson.toString),
                            partyId = partyId,
                            ultimateSendingPartyId = Some(baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId),
                            transferId = transferId,
                            isAccountValidated = Some(isAccountValidated),
                            pennyDropStatus = transferStatus,
                            pennyDropMode = pennyDropMode,
                            stateDetailsMessage = stateMessage,
                            stateDetailsReason = stateReason,
                            stateDetailsReasonCode = stateReasonCode)
                    }
                case _ =>
                    metrics.increment(s"trice.transfer.response.parsing.failed")
                    logger.info(s"Trice Logger : Transfer Response parsing failed for TxnID: ${txnId}")
                    //For 201 status, response would be there. This case won't happen.
                    TriceIntelligenceResponse(
                        lastApiCalled = Some("smart_verify"),
                        response = Some(smartVerifyResponse.response),
                        partyId = Some(""),  //We don't know the party id since now the party id and rtp transfer is part of the same response
                        ultimateSendingPartyId = Some(baseExternalAIVendorRequest.vendorConfigs("trice").ultimateSendingPartyId),
                        pennyDropMode = None,
                        errorMessage = smartVerifyResponse.errorMessage,
                        errorPath = smartVerifyResponse.errorPath
                    )
            }
        }
    }

    private def domestic(pii: Pii) = {
        if ( (pii.country.nonEmpty && pii.country.equalsIgnoreCase("us")) || (pii.accountCountry.nonEmpty && pii.accountCountry.equalsIgnoreCase("us")) )  {
            true
        } else {
            false
        }
    }

    private def accountHolderType(pii: Pii): String = {
        if (pii.entityName.nonEmpty) {
            "business"
        } else {
            "individual"
        }
    }

    def parseSmartTransferResponse(baseExternalAIVendorRequest: ExternalAIVendorRequest, smartTransferResponse: TriceAPIResponse, partyId: Option[String], ultimateSendingPartyId: Option[String], pennyDropMode: Option[String] = Some("rtp"), uri: String)(implicit trxId: TrxId): TriceIntelligenceResponse = {
        val txnId = baseExternalAIVendorRequest.transactionId
        if(TRICE_API_HTTP_SUCCESS_CODES.contains(smartTransferResponse.httpStatusCode)) {
            smartTransferResponse.responseAsJson match {
                case Some(smartTransferCreateResponseJson) =>
                    val transferStatus = (smartTransferCreateResponseJson \ "status").extractOpt[String]
                    val isAccountValidated = transferStatus.exists(TRANSFER_SUCCESS_STATUS.equalsIgnoreCase)
                    val transferId = (smartTransferCreateResponseJson \ "id").extractOpt[String]
                    val stateDetails = (smartTransferCreateResponseJson \ "state_details")
                    val lastSeenDate: Option[String] = (smartTransferCreateResponseJson \ "enrollment" \ "created").extractOpt[String].map(_.substring(0, 10))
                    val (stateMessage, stateReason, stateReasonCode) = stateDetails match {
                        case jVal: JValue => ((stateDetails \ "message").extractOpt[String], (stateDetails \ "reason").extractOpt[String], (stateDetails \ "reason_code").extractOpt[String])
                        case _ => (None, None, None)
                    }
                    TriceIntelligenceResponse(
                        statusCode = 200,
                        lastApiCalled = Some(uri),
                        response = Some(smartTransferResponse.response),
                        partyId = partyId,
                        ultimateSendingPartyId = ultimateSendingPartyId,
                        transferId = transferId,
                        isAccountValidated = Some(isAccountValidated),
                        pennyDropStatus = transferStatus,
                        pennyDropMode = pennyDropMode,
                        stateDetailsMessage = stateMessage,
                        stateDetailsReason = stateReason,
                        stateDetailsReasonCode = stateReasonCode,
                        lastSeenDate = lastSeenDate
                    )
                case _ => {
                    metrics.increment(s"trice.${pennyDropMode.get}.transfer.response.parsing.failed")
                    logger.info(s"Trice Logger : ${pennyDropMode.get} Transfer Response parsing failed for TxnID: ${txnId}")
                    //For 201 status, response would be there. This case won't happen.
                    TriceIntelligenceResponse(
                        lastApiCalled = Some("rtp_transfer"),
                        response = Some(smartTransferResponse.response),
                        partyId = partyId,
                        ultimateSendingPartyId = ultimateSendingPartyId,
                        pennyDropMode = pennyDropMode,
                        errorMessage = smartTransferResponse.errorMessage,
                        errorPath = smartTransferResponse.errorPath
                        )
                }
            }
        }
        else {
            metrics.increment(s"trice.${pennyDropMode.get}.transfer.request.failed")
            logger.info(s"Trice Logger: ${pennyDropMode.get} Transfer Request failed with http status code, ${smartTransferResponse.httpStatusCode} for TxnID: ${txnId}")
            TriceIntelligenceResponse(
                partyId = partyId,
                ultimateSendingPartyId = ultimateSendingPartyId,
                response = Some(smartTransferResponse.response),
                errorMessage = smartTransferResponse.errorMessage,
                errorPath = smartTransferResponse.errorPath
                )
        }
    }

    def retrieveSmartTransferDetails(baseExternalAIVendorRequest: ExternalAIVendorRequest, triceIntelligenceResponse: TriceIntelligenceResponse)(implicit trxId: TrxId): Future[TriceIntelligenceResponse] = {
        val uri = triceIntelligenceResponse.pennyDropMode match {
            case Some(rtpString) if rtpString.equalsIgnoreCase(TRANSFER_TYPE_RTP) =>
                TRANSFER_TYPE_RTP_API
            case Some(fedNowString) if fedNowString.equalsIgnoreCase(TRANSFER_TYPE_FEDNOW) =>
                TRANSFER_TYPE_FEDNOW_API
            case None =>
                logger.warn(s"Trice Logger: Penny drop mode unavailable for status check so defaulting to rtp status API for TxnID:${baseExternalAIVendorRequest.transactionId}")
                TRANSFER_TYPE_RTP_API
        }

        //calls rtp or fednow transfer api depending on what was used in the smart verify call
        val vendorConfigs =
            Map(
                "trice" -> VendorConfig(
                    uri = uri + triceIntelligenceResponse.transferId.getOrElse(""),
                    http_method = "get",
                    template = "trice.body.retrieve-rtp-transfer",
                    memo = "",
                    partyId = "",
                    ultimateSendingPartyId = ""
                    ))
        val externalAIVendorRequest = baseExternalAIVendorRequest.copy(vendorConfigs = vendorConfigs)
        externalAPICaller.getVendorData(externalAIVendorRequest).map {
            triceApiResponse => parseSmartTransferResponse(baseExternalAIVendorRequest = baseExternalAIVendorRequest, smartTransferResponse = triceApiResponse, partyId = triceIntelligenceResponse.partyId, ultimateSendingPartyId = triceIntelligenceResponse.ultimateSendingPartyId, uri = uri)
        }
    }

    def fetchTriceEnrollmentDetails: Future[Either[ErrorResponse, TriceEnrollmentResponse]] = {
        implicit val trxId = TrxId("fetch-trice-enrolment-details")
        val pii = Pii(
            firstName = "",
            surName = "",
            entityName = "",
            dob = "",
            accountNumber = "",
            routingNumber = "",
            accountCountry = "",
            streetAddress = "",
            streetAddress2 = "",
            city = "",
            state = "",
            country = "",
            zipCode = ""
            )
        val vendorConfig = Map(
            "trice" -> VendorConfig(
                uri = "/hub/v1/account",
                http_method = "get",
                template = "trice.body.retrieve-account",
                memo = "",
                partyId = "",
                ultimateSendingPartyId = ""
                ))
        val externalAIVendorRequest = ExternalAIVendorRequest(
            transactionId = UUID.randomUUID().toString,
            accountId = "0",
            piis = pii,
            vendorConfigs = vendorConfig,
            maskPiiEnabled = true
            )
        externalAPICaller.getVendorData(externalAIVendorRequest).map {
            accountDetailsResponse =>
                val response = accountDetailsResponse.response
                if(TRICE_API_HTTP_SUCCESS_CODES.contains(accountDetailsResponse.httpStatusCode)) {
                    accountDetailsResponse.responseAsJson match {
                        case Some(accountDetailsResponseJson) =>
                            val enrollmentId = ((accountDetailsResponseJson \ "enrollments")(0) \ "id").extract[String]
                            Right(TriceEnrollmentResponse(enrollmentId = enrollmentId))

                        case _ => {
                            //For 201 status, response would be there. This case won't happen.
                            logger.info(s"Trice Logger : Account details Response parsing failed with response: $response")
                            Left(ErrorResponse(500, s"Trice Account details fetch failed with response: $response"))
                        }
                    }
                }
                else {
                    logger.error(s"Trice Logger : Account details fetch failed with http status code, ${accountDetailsResponse.httpStatusCode} and response $response")
                    Left(ErrorResponse(500, s"Trice Account details fetch failed with response: $response"))
                }
        }
    }

    def createOrUpdateUltimateSendingParty(triceUltimateSendingPartyCreateRequest: TriceUltimateSendingPartyCreateOrUpdateRequest): Future[Either[ErrorResponse, TriceUltimateSendingPartyDetails]] = {
        implicit val trxId = TrxId("create-or-update-ultimate-sending-party")
        val pii = Pii(
            firstName = "",
            surName = "",
            entityName = triceUltimateSendingPartyCreateRequest.depositorName,
            dob = "",
            accountNumber = "",
            routingNumber = "",
            accountCountry = "",
            streetAddress = triceUltimateSendingPartyCreateRequest.physicalAddress,
            streetAddress2 = triceUltimateSendingPartyCreateRequest.physicalAddress2.getOrElse(""),
            city = triceUltimateSendingPartyCreateRequest.city,
            state = triceUltimateSendingPartyCreateRequest.state,
            country = triceUltimateSendingPartyCreateRequest.country,
            zipCode = triceUltimateSendingPartyCreateRequest.zip
            )
        val (uri, httpMethod) = triceUltimateSendingPartyCreateRequest.partyId match {
            case Some(partyId) if partyId.nonEmpty => ("/hub/v1/parties/" + partyId, "put")
            case _ => ("/hub/v1/parties", "post")
        }
        logger.info(s"Proceeding to upsert Ultimate Sending Party ID. URI: $uri HTTPMethod: $httpMethod Request: $triceUltimateSendingPartyCreateRequest")
        val vendorConfig = Map(
            "trice" -> VendorConfig(
                uri = uri,
                http_method = httpMethod,
                template = "trice.body.create-ultimate-sending-party",
                memo = "",
                partyId = "",
                ultimateSendingPartyId = ""
                ))
        val externalAIVendorRequest = ExternalAIVendorRequest(
            transactionId = UUID.randomUUID().toString,
            accountId = triceUltimateSendingPartyCreateRequest.accountId,
            piis = pii,
            vendorConfigs = vendorConfig,
            maskPiiEnabled = true
            )
        externalAPICaller.getVendorData(externalAIVendorRequest).map {
            createOrUpdatePartyResponse =>
                val response = createOrUpdatePartyResponse.response
                if(TRICE_API_HTTP_SUCCESS_CODES.contains(createOrUpdatePartyResponse.httpStatusCode)) {
                    createOrUpdatePartyResponse.responseAsJson match {
                        case Some(partyCreateResponseJson) =>
                            val partyStatus = (partyCreateResponseJson \ "status").extract[String]
                            val partyId = (partyCreateResponseJson \ "id").extract[String]
                            if(PARTY_SUCCESS_STATUS.equalsIgnoreCase(partyStatus)) {
                                val name = (partyCreateResponseJson \ "name").extract[String]
                                val address = (partyCreateResponseJson \ "address")
                                val line1 = (address \ "line1").extract[String]
                                val line2 = (address \ "line2").extract[String]
                                val city = (address \ "city").extract[String]
                                val state = (address \ "state").extract[String]
                                val zip = (address \ "zip").extract[String]
                                val country = (address \ "country").extract[String]
                                Right(
                                    TriceUltimateSendingPartyDetails(
                                        partyId = partyId,
                                        status = partyStatus,
                                        depositorName = name,
                                        physicalAddress = line1,
                                        physicalAddress2 = line2,
                                        city = city,
                                        state = state,
                                        zip = zip,
                                        country = country
                                        )
                                    )
                            }
                            else {
                                if(PARTY_ON_HOLD_STATUS.equalsIgnoreCase(partyStatus)) {
                                    val holdDetails = (partyCreateResponseJson \ "hold_details")
                                    val holdMessage = (holdDetails \ "message").extractOpt[String]
                                    val holdReason = (holdDetails \ "reason").extractOpt[String]
                                    logger.info(s"Trice Logger : Ultimate Sending Party creation failed as Party status is not 'ok'. HoldDetailsMessage: $holdMessage HoldDetailsReason: $holdReason")
                                    Left(ErrorResponse(500, "Trice Ultimate Sending Party creation failed due to on_hold"))
                                }
                                else {
                                    logger.info(s"Trice Logger : Ultimate Sending Party creation failed as Party status is unknown. PartyStatus: $partyStatus Response: $response")
                                    Left(ErrorResponse(500, s"Trice Ultimate Sending Party creation failed as Party status is unknown with response: $response"))
                                }
                            }
                        case _ => {
                            //For 201 status, response would be there. This case won't happen.
                            logger.info(s"Trice Logger : Ultimate Sending Party Response parsing failed with response $response")
                            Left(ErrorResponse(500, s"Trice Ultimate Sending Party Response parsing failed with response $response"))
                        }
                    }
                }
                else {
                    logger.error(s"Trice Logger : Ultimate Sending Party creation request failed with http status code, ${createOrUpdatePartyResponse.httpStatusCode} and response, ${createOrUpdatePartyResponse.response}")
                    Left(ErrorResponse(500, s"Trice Ultimate Sending Party creation request failed with response $response"))
                }
        }
    }

    private def extractDob(dobStr: String): Option[DateTime] = {
        @tailrec
        def extract0(formats: List[DateTimeFormatter]): Option[DateTime] = {
            formats match {
                case Nil => None
                case format :: restOfTheFormats => Try(format.parseDateTime(dobStr)) match {
                    case Success(dob) => Some(dob)
                    case Failure(_) => extract0(formats = restOfTheFormats)
                }
            }
        }

        extract0(formats = dobFormats)
    }

    private def generateCacheKey(baseExternalAIVendorRequest: ExternalAIVendorRequest): String = {
        s"${baseExternalAIVendorRequest.piis.routingNumber}-${baseExternalAIVendorRequest.piis.accountNumber}"
    }

    private def cacheIfNeeded(baseExternalAIVendorRequest: ExternalAIVendorRequest, triceIntelligenceResponse: TriceIntelligenceResponse, cacheKey: Option[String])(implicit trxId: TrxId): Unit = {
        if (triceIntelligenceResponse.pennyDropStatus.exists(POSSIBLE_TRANSFER_PENDING_STATUS.contains) && cacheKey.isEmpty) {
            cacheService.set(
                generateCacheKey(baseExternalAIVendorRequest),
                CacheValue(
                    transferId = triceIntelligenceResponse.transferId.getOrElse(""),
                    transferType = triceIntelligenceResponse.pennyDropMode.getOrElse(TRANSFER_TYPE_RTP),
                    transferStatus = triceIntelligenceResponse.pennyDropStatus
                )
            )
        }
    }
}
