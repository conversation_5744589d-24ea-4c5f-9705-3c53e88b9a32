package me.socure.ai.trice.servlet

import com.google.inject.Inject
import me.socure.ai.gateway.common.models.vendor.AIVendorRequest
import me.socure.ai.trice.model.TriceUltimateSendingPartyCreateOrUpdateRequest
import me.socure.ai.trice.service.TriceVerificationService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.{ErrorResponse, ResponseStatus}
import org.json4s.{DefaultFormats, Formats}
import org.json4s.ext.EnumNameSerializer
import org.scalatra.{FutureSupport, ScalatraServlet}
import org.scalatra.json.JacksonJsonSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class TriceAPIServlet @Inject()(triceVerificationService: TriceVerificationService)(implicit val executor: ExecutionContext) extends
    ScalatraServlet with JacksonJsonSupport with FutureSupport {

    override protected implicit def jsonFormats: Formats = DefaultFormats ++ Seq(new EnumNameSerializer(ResponseStatus))

    val logger: Logger = LoggerFactory.getLogger(getClass)
    private val metrics: Metrics = JavaMetricsFactory.get(getClass)

    before() {
        contentType = formats("json")
        //validateRequest()
    }

    post("/account/intelligence") {
        try {
            val req = parsedBody.extract[AIVendorRequest]
            ScalatraResponseFactory.get {
                metrics.timeFuture("request.validate") {
                    triceVerificationService.process(req)
                }
            }
        }
        catch {
            case ex: Exception => {
                metrics.increment("invalid.request")
                logger.error("Failed to parse user input", ex)
                ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
            }
        }
    }

    get("/fetch/enrollmentid") {
        try {
            ScalatraResponseFactory.get {
                triceVerificationService.fetchTriceEnrollmentDetails
            }
        }
        catch {
            case ex: Exception => {
                logger.error("Failed to parse user input", ex)
                ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
            }
        }
    }

    post("/upsert/ultimatesendingparty") {
        try {
            val req = parsedBody.extract[TriceUltimateSendingPartyCreateOrUpdateRequest]
            ScalatraResponseFactory.get {
                triceVerificationService.createOrUpdateUltimateSendingParty(req)
            }
        }
        catch {
            case ex: Exception => {
                logger.error("Failed to parse user input for ultimatesendingparty upsert due to ", ex)
                ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
            }
        }
    }

}
