### Steps to deploy Pod to EKS using Helm

#### Clone this git repo

This repo has common template for helm chars.

https://gitlab-ee.us-east-vpc.socure.be/infrastructure/helm-charts/-/tree/main/product/socure-backend-service

Navigate to the `product` directory in the cloned repo.

Now run the following helm command to deploy the ai-gateway service to the EKS cluster under the
namespace `account-intelligence`

```
helm upgrade -install ai-gateway ./socure-backend-service -n account-intelligence -f ~/socuregit/engg/ai-gateway/helm/values-dev.yaml
```

Successful run output would look like this

```
$ helm upgrade -install ai-gateway ./socure-backend-service -n account-intelligence -f ~/socuregit/engg/ai-gateway/helm/values-dev.yaml
Release "ai-gateway" does not exist. Installing it now.
NAME: ai-gateway
LAST DEPLOYED: Mon Sep 19 22:51:44 2022
NAMESPACE: account-intelligence
STATUS: deployed
REVISION: 1
NOTES:
1. Get the application URL by running these commands:
  http://socure-ai-gateway.eks.us-east-1.product-dev.socure.link/

You have deployed the following release: ai-gateway.
To get further information, you can run the commands:
  $ helm status ai-gateway
  $ helm get all ai-gateway
```
