image:
  repository: fips-registry.us-east-1.build.socure.link/ai/ai-gateway-service
  tag: "OVERRIDE_ME"

serviceAccount:
  name: "ai-gateway-service-dev"
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-irsa-aa0f93c-dev-a26cf77c

application:
  env:
    CONFIGURATION_NAME: "ai-gateway-service"
    CONFIGURATION_VERSION: "OVERRIDE_ME"
    DD_CONSTANT_TAGS: "ddname:ai-gateway-service"

deployment:
  resources:
    limits:
      cpu: "3"
      memory: "10Gi"
    requests:
      cpu: "500m"
      memory: "4Gi"

istio:
  enabled: true
  hosts:
  - ai-gateway-service.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - "idplus-service-dev"
        - "account-service-dev"
        - "ai-gateway-service-dev"
