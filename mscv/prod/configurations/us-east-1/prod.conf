threadpool {
  poolSize = 150
}

scheduler {
  poolSize=100
}

server {
  port = 5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 80%
    non_heap.used_max_percentage = "<80.0" //should be less than 80%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

hmac {
  ttl = 5
  time.interval = 5
  strength = 512
  aws.secrets.manager.id = "ai-gateway-service/prod/hmac-5a707e7c"
  secret.refresh.interval = 5000
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]
jmx {
  port = 1098
}

vendor.services = [
  {
    vendorName = "BNYVL"
    endpoint = "http://ai-bnymellon-service"
    apiName = "/account/validate"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "ai-bnymellon-service/prod/hmac-a8717596"
      secret.refresh.interval = 5000
    }
    grpc {
      host = "ai-bnymellon-service.webapps.us-east-1.prod.socure.link"
      port = "443"
      useTLS = true
    }
    use.grpc = false
  },
  {
    vendorName = "MBTVL"
    endpoint = "http://ai-microbilt-service"
    apiName = "/account/validate"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "ai-microbilt-service/prod/hmac-19208f46"
      secret.refresh.interval = 5000
    }
    grpc {
      host = "ai-microbilt-service.webapps.us-east-1.prod.socure.link"
      port = "443"
      useTLS = true
    }
    use.grpc = false
  },
  {
    vendorName = "SOCVL"
    endpoint = "http://ai-socid-service"
    apiName = "/search"
    hmac {
      realm = "Socure"
      version = "1.0"
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id = "ai-socid-service/prod/hmac-18eb7380"
      secret.refresh.interval = 5000
    }
    grpc {
      host = "ai-socid-service.webapps.us-east-1.prod.socure.link"
      port = "443"
      useTLS = true
    }
    use.grpc = false
  },
  {
    vendorName = "TRICE"
    endpoint = "http://ai-gateway-service"
    apiName = "/trice/account/intelligence"
    use.grpc = false
  },
     {
       vendorName = "CONVL"
       endpoint = "http://ai-gateway-v2-service"
       apiName = "/convl-lookup"
       use.grpc = false
     },
            {
                vendorName = "VRVAL"
                endpoint = "http://ai-gateway-v2-service"
                apiName = "/vrval-lookup"
                use.grpc = false
             }
]

#================ AI Vendor Service config ================#
ai.vendor.service {
  api = "http://ai-vendor-service/vendor/data"
}

ai.gateway.v2.service {
  endpoint = "http://ai-gateway-v2-service"
}

trice {
  maxPollCount = 6
  maxPollTimeoutInMillis = 6000
  sleepBetweenPollInMillis = 1000
}

#================ Reasoncode Service config ================#
reasoncode.service {
  endpoint = "http://reasoncode-service"
  endpoint2 = "http://reasoncode-service"
  hmac {
    secret.key = """ENC(fFysuWDbCbRbxj8fDK+wYj0vq9RQCOtgyS99QtUAwCEO3rXkiPYGp/9g+Axjxlwsq1UWUBltl+IxAPrLVd5Oqw==)""" # FIPS encrypted value
    strength = 512
    realm = "Socure"
    version = "1.0"
  }
  cache {
    refreshIntervalInMinutes = 15
    retry {
      initialTimeoutMillis = 100
      maxAttempts = 2
    }
  }
}

#================ # Transaction Auditing Conf ================#
transaction-auditing {
  threadpool {
    poolSize = 30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region = us-east-1
        transaction {
          queueName = transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region = us-west-2
        transaction {
          queueName = transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region = us-east-2
        transaction {
          queueName = transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        third-party {
          queueName = third-party-transaction-auditing-prod
          waitTimeSeconds = 20
          maxBatchSize = 10
          maxBufferSize = 60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder = "sqs-storage-prod"
      }
      third-party {
        region = us-east-1
        bucket = "thirdparty-stats-prod"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

#================ Reasoncode Service config ================#
context {
  cache {
    size = 5000
    threads = 50
  }
}

# Cache for Trice Transfer Response #
cache {
  # DynamoDB Based Cache #
  dynamo {
    table = "sai_trice_transfer_cache_prod"
    partitionKey = "id"
    region = "us-east-1"
    # In hours
    ttlKey = "expiresOn"
    ttl = "72"
  }
}

# === Dynamic Control Center V2 Config === #
dynamic.control.center {
  s3 {
    bucketName = "globalconfig-prod"
  }
  memcached {
    host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}
# === End === #
