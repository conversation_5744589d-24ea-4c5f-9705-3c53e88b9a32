{"version": "v2", "vendor_variables": [{"variable_name": "vendor_vars", "operators": [{"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100007"}], "output": "bnyvl_bus_name_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100018"}], "output": "bnyvl_address_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100009"}], "output": "bnyvl_dob_rulecode"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "BNYVL_100002"}], "output": "bnyvl_asv_description"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100009"}], "output": "mbtvl_property_message"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100010"}], "output": "mbtvl_returns_thr"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "MBTVL_100011"}], "output": "mbtvl_last_seen_multi_thr"}, {"name": "lookup", "inputs": ["input.ruleCodes.categoricalRulecodes_optional_"], "options": [{"name": "attr", "value": "TRICE_100005"}], "output": "trice_asv_decision"}]}], "rules": [{"rule_code_name": "GLOBAL.300870", "description": "GLOBAL_SAI_ROUTING_NUMBER_INVALID", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.bnyvl_asv_description"], "methods": ["clean_string", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___invalid abart__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "bnyvl_invalid_routing_rulecode"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["clean_string", "trim"], "output": "clean_local_rule_code"}], "inputs": ["clean_local_rule_code", "___routing number is invalid__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_invalid_routing_rulecode"}, {"name": "if_else", "preprocessors": [], "inputs": ["bnyvl_invalid_routing_rulecode", "mbtvl_invalid_routing_rulecode", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "invalid_routing_rulecode"}]}, {"rule_code_name": "GLOBAL.300872", "description": "GLOBAL_ACCOUNT_WARNING", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["clean_string", "trim"], "output": "clean_property_message"}], "inputs": ["clean_property_message", "___history of returns no current unpaid items__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_property_message_unpaid"}, {"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_property_message"], "methods": ["trim", "clean_string"], "output": "clean_property_message"}], "inputs": ["clean_property_message", "___neg__as_string__", "___true__as_boolean__", "___false__as_boolean__", "___false__as_boolean__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "mbtvl_property_message_neg"}, {"name": "if_else", "inputs": ["mbtvl_property_message_unpaid", "mbtvl_property_message_neg", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "||"}, {"name": "dataType", "value": "boolean"}], "output": "global_account_warning_rulecode"}]}, {"rule_code_name": "GLOBAL.300891", "description": "GLOBAL_RETURN_HISTORY_INDICATOR", "type": "numerical", "default": "NA", "operators": [{"name": "if_else", "preprocessors": [{"inputs": ["vendor.vendor_vars.mbtvl_returns_thr"], "methods": ["trim", "lowercase"], "output": "clean_returns_thr"}], "inputs": ["clean_returns_thr", "___yes__as_string__", "___1__as_int__", "_____as_<PERSON>A__"], "options": [{"name": "condition", "value": "=="}, {"name": "dataType", "value": "string"}], "output": "global_return_history_rulecode"}]}]}