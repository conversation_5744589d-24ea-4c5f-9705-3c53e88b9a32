<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>maven-root</artifactId>
        <groupId>me.socure</groupId>
        <version>0.2-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>ai-gateway</artifactId>
    <version>${revision}</version>
    <modules>
        <module>ai-gateway-rest</module>
        <module>ai-gateway-client</module>
        <module>ai-gateway-service</module>
        <module>ai-gateway-common</module>
        <module>ai-gateway-vendor-service</module>
        <module>ai-gateway-rulecode</module>
        <module>ai-gateway-reasoncode</module>
        <module>ai-gateway-grpc</module>
        <module>ai-gateway-protobuf</module>
        <module>ai-trice-service</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <version>${revision}</version>
    </properties>

    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci-fips</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <allowInsecureRegistries>true</allowInsecureRegistries>
                            <from>
                                <image>${env.JIB_JDK8_FIPS_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}
                                </image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest-fips</tag>
                                    <tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.ai.gateway.service.Main</mainClass>
                                <jvmFlags>
                                    <jvmFlag>-server</jvmFlag>
                                </jvmFlags>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>jib</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>amazoncorretto:8u342-al2</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}
                                </image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.ai.gateway.service.Main</mainClass>
                                <jvmFlags>
                                    <jvmFlag>-server</jvmFlag>
                                </jvmFlags>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.thesamet.scalapb</groupId>
                <artifactId>scalapb-runtime-grpc_2.11</artifactId>
                <version>0.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.thesamet.scalapb</groupId>
                <artifactId>scalapb-json4s_2.11</artifactId>
                <version>0.9.3</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>4.28.2</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>1.63.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>1.48.1</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty</artifactId>
                <version>1.63.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>1.48.1</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-services</artifactId>
                <version>1.63.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-api</artifactId>
                <version>1.48.1</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-context</artifactId>
                <version>1.48.1</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.12.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.15.0-rc1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
